// Location Service Class
class LocationService {
    constructor() {
        this.cachedLocation = null;
        this.cacheExpiry = null;
        this.cacheTimeout = 30 * 60 * 1000; // 30 minutes cache
    }

    async getCurrentLocation() {
        // Check if we have valid cached data
        if (this.cachedLocation && this.cacheExpiry && Date.now() < this.cacheExpiry) {
            return this.cachedLocation;
        }

        try {
            // Get user's coordinates
            const position = await this.getUserPosition();
            if (!position) {
                return null;
            }

            // Get location details using reverse geocoding
            const locationData = await this.reverseGeocode(position.coords.latitude, position.coords.longitude);

            // Cache the result
            this.cachedLocation = locationData;
            this.cacheExpiry = Date.now() + this.cacheTimeout;

            return locationData;
        } catch (error) {
            console.warn('Location service error:', error);
            return null;
        }
    }

    getUserPosition() {
        return new Promise((resolve, reject) => {
            if (!navigator.geolocation) {
                reject(new Error('Geolocation is not supported by this browser'));
                return;
            }

            navigator.geolocation.getCurrentPosition(
                (position) => resolve(position),
                (error) => {
                    console.warn('Geolocation error:', error);
                    resolve(null); // Don't reject, just return null
                },
                {
                    enableHighAccuracy: false,
                    timeout: 10000,
                    maximumAge: 600000 // 10 minutes
                }
            );
        });
    }

    async reverseGeocode(lat, lon) {
        try {
            // Using OpenStreetMap's Nominatim service (free, no API key required)
            const url = `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lon}&zoom=10&addressdetails=1`;

            const response = await fetch(url, {
                headers: {
                    'User-Agent': 'Veritas Chat AI (Educational Use)'
                }
            });

            if (!response.ok) {
                throw new Error(`Geocoding API error: ${response.status}`);
            }

            const data = await response.json();

            // Format the location data for the AI
            return {
                coordinates: {
                    latitude: parseFloat(lat).toFixed(4),
                    longitude: parseFloat(lon).toFixed(4)
                },
                city: data.address?.city || data.address?.town || data.address?.village || 'Unknown',
                state: data.address?.state || data.address?.province || '',
                country: data.address?.country || 'Unknown',
                countryCode: data.address?.country_code?.toUpperCase() || '',
                region: data.address?.region || '',
                displayName: data.display_name || 'Unknown Location',
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
            };
        } catch (error) {
            console.warn('Reverse geocoding failed:', error);
            // Return basic location with coordinates only
            return {
                coordinates: {
                    latitude: parseFloat(lat).toFixed(4),
                    longitude: parseFloat(lon).toFixed(4)
                },
                city: 'Unknown',
                state: '',
                country: 'Unknown',
                countryCode: '',
                region: '',
                displayName: `Location: ${parseFloat(lat).toFixed(4)}, ${parseFloat(lon).toFixed(4)}`,
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
            };
        }
    }

    formatLocationForAI(locationData) {
        if (!locationData) return '';

        const locationParts = [locationData.city];
        if (locationData.state) locationParts.push(locationData.state);
        if (locationData.country) locationParts.push(locationData.country);

        const locationString = locationParts.join(', ');

        // DO NOT expose precise coordinates to the AI for privacy/security
        return `**Current Location Information:**
- Location: ${locationString}
- Timezone: ${locationData.timezone}
- Country Code: ${locationData.countryCode}
- Region: ${locationData.region || 'Unknown'}

Note: Precise coordinates are not provided for privacy and security reasons.`;
    }
}

class ChatApp {
    constructor() {
        this.currentChatId = null;
        this.chats = new Map();
        this.isTyping = false;
        this.sidebarVisible = true;
        this.apiConfigured = CONFIG.GEMINI_API_KEY && CONFIG.GEMINI_API_KEY.trim() !== '';
        this.conversationsLoaded = false;

        // Scroll behavior management
        this.userScrolledUp = false;
        this.isAutoScrolling = false;
        this.scrollThreshold = 50; // pixels from bottom to consider "at bottom"

        // Event handler tracking for cleanup
        this.globalClickHandler = null;

        // Prevent duplicate message sending
        this.isSendingMessage = false;

        // Dynamic placeholder texts
        this.placeholderTexts = [
            "Ask me anything... ✨",
            "What's on your mind? 💭",
            "How can I help you today? 🤖",
            "Share your thoughts... 💡",
            "Let's explore ideas together... 🚀",
            "What would you like to know? 🔍"
        ];
        this.currentPlaceholderIndex = 0;

        // Initialize services
        this.supabaseService = SupabaseService.getInstance();
        this.useDatabase = this.supabaseService.isAvailable();
        this.locationService = new LocationService();

        // Initialize multi-agent system
        this.multiAgentSystem = new MultiAgentSystem();
        this.useMultiAgent = false; // Can be toggled by user or automatically determined
        this.requestedAgents = []; // Track agents requested by orchestrator for rotation

        // Multi-agent loading messages
        this.multiAgentLoadingMessages = [
            "🧠 AI minds collaborating on your request...",
            "⚡ Orchestrating intelligent response...",
            "🔗 Weaving multi-agent insights...",
            "🤖 Coordinating specialized AI agents...",
            "✨ Neural networks harmonizing...",
            "🎯 Agents assembling optimal solution...",
            "🌟 Intelligence constellation aligning...",
            "🔄 Synthesizing collaborative intelligence...",
            "🚀 Multi-dimensional thinking in progress...",
            "💫 Quantum minds converging...",
            "⚙️ Cognitive engines synchronizing...",
            "🔥 Collaborative AI in motion..."
        ];
        this.currentLoadingMessageIndex = 0;
        this.loadingMessageInterval = null;

        // Input height preservation for expanded state
        this.preservedInputHeight = null;

        this.initializeElements();
        this.bindEvents();
        this.checkAPIConfiguration();
        this.initializePlaceholderRotation();
        this.loadMultiAgentPreference();

        // New lightweight cleanup setup
        this.setupCleanupHandlers();

        // Refresh message formatting to apply any updates
        this.refreshMessageFormatting();

        // Don't load conversations immediately - wait for authentication to be fully established
    }

    // Ensure conversations are loaded after authentication is established
    async ensureConversationsLoaded(progressCallback = null) {
        if (!this.conversationsLoaded) {
            console.log('Loading conversations after authentication...');
            if (progressCallback) progressCallback('Loading conversation history...', 60);
            await this.loadConversationsFromDatabase();
            if (progressCallback) progressCallback('Setting up chat interface...', 80);
            this.conversationsLoaded = true;
            console.log('Conversations loaded successfully');
        }
    }

    // Reload conversations (useful when user changes or auth state changes)
    async reloadConversations(progressCallback = null) {
        console.log('Reloading conversations...');
        this.conversationsLoaded = false;
        this.chats.clear();
        this.currentChatId = null;
        await this.ensureConversationsLoaded(progressCallback);
    }

    initializeElements() {
        this.sidebar = document.getElementById('sidebar');
        this.sidebarToggle = document.getElementById('sidebarToggle');
        this.sidebarBackdrop = document.getElementById('sidebarBackdrop');
        this.newChatBtn = document.getElementById('newChatBtn');
        this.newChatBtnHeader = document.getElementById('newChatBtnHeader');
        this.multiAgentToggle = document.getElementById('multiAgentToggle');
        this.chatContainer = document.getElementById('chatContainer');
        this.chatHistory = document.getElementById('chatHistory');
        this.messageInput = document.getElementById('messageInput');
        this.sendBtn = document.getElementById('sendBtn');
        this.uploadBtn = document.getElementById('uploadBtn');
        this.fileInput = document.getElementById('fileInput');
        this.mainContent = document.querySelector('.main-content');

        // Initialize file upload properties
        this.supportedFileTypes = {
            'application/pdf': 'PDF',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word',
            'application/msword': 'Word',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Excel',
            'application/vnd.ms-excel': 'Excel',
            'text/plain': 'Text',
            'text/csv': 'CSV',
            'application/json': 'JSON'
        };
        this.maxFileSize = 10 * 1024 * 1024; // 10MB limit

        // Store pending document data
        this.pendingDocuments = null;

        // Listener attachment flags removed
    }

    // Get current user ID for database operations
    async getCurrentUserId() {
        if (this.supabaseService.isAvailable()) {
            const { data: { user } } = await this.supabaseService.supabase.auth.getUser();
            return user ? user.id : null;
        } else {
            // Fallback to localStorage user
            const currentUser = localStorage.getItem('currentUser');
            if (currentUser) {
                return JSON.parse(currentUser).id;
            }
            return null;
        }
    }

    bindEvents() {
        // New simplified event listeners
        this.attachUIListeners();
        this.attachInputListeners();
        this.attachChatListeners();

        // Initialize sidebar state after elements are bound
        this.initializeSidebarState();
    }

    attachUIListeners() {
        // Sidebar toggle with modern approach
        if (this.sidebarToggle) {
            this.sidebarToggle.onclick = () => this.toggleSidebar();
        }

        // Sidebar backdrop click to close on mobile
        if (this.sidebarBackdrop) {
            this.sidebarBackdrop.onclick = () => this.closeSidebar();
        }

        // Handle window resize for responsive behavior
        window.addEventListener('resize', () => {
            this.handleResize();
            // Re-initialize sidebar state on resize to handle mobile/desktop transitions
            this.initializeSidebarState();
        });

        // Handle viewport height changes for mobile browsers
        this.setupViewportHandler();

        // Chat management buttons
        if (this.newChatBtn) {
            this.newChatBtn.onclick = () => this.createNewChat();
        }

        if (this.newChatBtnHeader) {
            this.newChatBtnHeader.onclick = () => this.createNewChat();
        }

        // Multi-agent toggle
        if (this.multiAgentToggle) {
            this.multiAgentToggle.onclick = () => this.toggleMultiAgentMode();
        }
    }

    attachInputListeners() {
        // Send button with direct onclick
        if (this.sendBtn) {
            this.sendBtn.onclick = () => this.sendMessage();
        }

        // Upload button with direct onclick
        if (this.uploadBtn) {
            this.uploadBtn.onclick = () => this.fileInput.click();
        }

        // File input change handler
        if (this.fileInput) {
            this.fileInput.onchange = (event) => this.handleFileUpload(event);
        }

        // Message input with modern event handling
        if (this.messageInput) {
            this.messageInput.oninput = () => {
                this.handleInputChange();
                this.autoResize();
            };

            this.messageInput.onkeydown = (event) => {
                if (event.key === 'Enter' && !event.shiftKey) {
                    event.preventDefault();
                    this.sendMessage();
                }
            };


        }
    }

    attachChatListeners() {
        // Remove any existing global click handler to prevent duplicates
        if (this.globalClickHandler) {
            document.removeEventListener('click', this.globalClickHandler);
        }

        // Example prompts with event delegation
        this.globalClickHandler = (event) => {
            const promptCard = event.target.closest('.prompt-card');
            if (promptCard && this.messageInput) {
                const prompt = promptCard.dataset.prompt;
                this.messageInput.value = prompt;
                this.handleInputChange();
                this.sendMessage();
            }
        };
        document.addEventListener('click', this.globalClickHandler);

        // Chat history with simplified delegation
        if (this.chatHistory) {
            this.chatHistory.onclick = (event) => {
                this.handleChatHistoryClick(event);
            };
        }

        // Chat container scroll listener for smart auto-scroll behavior
        if (this.chatContainer) {
            this.chatContainer.onscroll = () => {
                this.handleChatScroll();
            };
        }
    }

    handleChatHistoryClick(event) {
        // Handle menu button clicks
        const menuButton = event.target.closest('.chat-menu-button');
        if (menuButton) {
            event.stopPropagation();
            this.toggleChatMenu(menuButton);
            return;
        }

        // Handle delete button clicks
        const deleteBtn = event.target.closest('.delete-chat-btn');
        if (deleteBtn) {
            event.stopPropagation();
            const chatId = deleteBtn.dataset.chatId;
            this.deleteChatWithConfirmation(chatId);
            return;
        }

        // Handle chat item selection
        const chatItem = event.target.closest('.chat-item');
        if (chatItem && !event.target.closest('.chat-dropdown-menu')) {
            const chatId = chatItem.dataset.chatId;
            this.switchToChat(chatId);
        }
    }

    toggleChatMenu(menuButton) {
        const chatItem = menuButton.closest('.chat-item');
        const dropdownMenu = chatItem.querySelector('.chat-dropdown-menu');

        // Close other menus
        document.querySelectorAll('.chat-dropdown-menu.show').forEach(menu => {
            if (menu !== dropdownMenu) {
                menu.classList.remove('show');
            }
        });

        // Position and toggle current menu
        const buttonRect = menuButton.getBoundingClientRect();
        const sidebarRect = document.getElementById('sidebar').getBoundingClientRect();

        dropdownMenu.style.top = `${buttonRect.top}px`;
        dropdownMenu.style.left = `${sidebarRect.right + 8}px`;
        dropdownMenu.classList.toggle('show');
    }

    toggleSidebar() {
        this.sidebarVisible = !this.sidebarVisible;

        if (this.sidebarVisible) {
            this.openSidebar();
        } else {
            this.closeSidebar();
        }
    }

    openSidebar() {
        this.sidebarVisible = true;
        this.sidebar.classList.remove('hidden');
        this.sidebar.classList.add('open');

        // Show backdrop on mobile
        if (this.isMobile()) {
            this.sidebarBackdrop.classList.add('show');
            // Prevent body scroll when sidebar is open on mobile
            document.body.style.overflow = 'hidden';
        } else {
            this.mainContent.classList.remove('sidebar-hidden');
        }

        // Update hamburger icon
        const icon = this.sidebarToggle.querySelector('i');
        icon.className = 'fas fa-times';
    }

    closeSidebar() {
        this.sidebarVisible = false;
        this.sidebar.classList.add('hidden');
        this.sidebar.classList.remove('open');

        // Hide backdrop and restore body scroll
        if (this.sidebarBackdrop) {
            this.sidebarBackdrop.classList.remove('show');
        }
        document.body.style.overflow = '';

        if (!this.isMobile()) {
            this.mainContent.classList.add('sidebar-hidden');
        }

        // Update hamburger icon
        const icon = this.sidebarToggle.querySelector('i');
        icon.className = 'fas fa-bars';
    }

    isMobile() {
        return window.innerWidth <= 768;
    }

    initializeSwipeGestures() {
        // Initialize swipe gesture properties
        this.touchStartX = 0;
        this.touchStartY = 0;
        this.touchEndX = 0;
        this.touchEndY = 0;
        this.isSwipeGesture = false;
        this.swipeThreshold = 50; // Minimum distance for a swipe
        this.swipeAngleThreshold = 30; // Maximum angle deviation for horizontal swipe

        // Initialize sidebar state based on current screen size and CSS state
        this.initializeSidebarState();

        // Try to get the main content element again
        this.mainContent = document.querySelector('.main-content');

        // Also try app container as fallback
        const appContainer = document.getElementById('appContainer');

        // Use app container if main content is not available
        const targetElement = this.mainContent || appContainer;

        if (targetElement) {
            // Add touch event listeners
            targetElement.addEventListener('touchstart', (e) => this.handleTouchStart(e), { passive: true });
            targetElement.addEventListener('touchmove', (e) => this.handleTouchMove(e), { passive: false });
            targetElement.addEventListener('touchend', (e) => this.handleTouchEnd(e), { passive: true });

            // Also add mouse events for desktop testing
            this.addMouseSwipeEvents(targetElement);

            console.log('Swipe gestures initialized successfully');
        } else {
            console.error('Failed to initialize swipe gestures - no suitable element found');
        }
    }

    initializeSidebarState() {
        // Set initial sidebar state based on screen size and current DOM state
        if (this.isMobile()) {
            // On mobile, sidebar starts hidden (matches CSS transform: translateX(-100%))
            this.sidebarVisible = false;

            // Ensure DOM state matches
            if (this.sidebar) {
                this.sidebar.classList.remove('open');
                this.sidebar.classList.add('hidden');
            }
        } else {
            // On desktop, sidebar starts visible
            this.sidebarVisible = true;

            // Ensure DOM state matches
            if (this.sidebar) {
                this.sidebar.classList.remove('hidden');
                this.sidebar.classList.remove('open');
            }
        }

        // Sidebar state initialized successfully
    }

    handleTouchStart(e) {
        // Only handle swipes on mobile devices
        if (!this.isMobile()) return;

        // Get the first touch point
        const touch = e.touches[0];
        this.touchStartX = touch.clientX;
        this.touchStartY = touch.clientY;
        this.isSwipeGesture = false;

        // Only start tracking if the touch starts near the left edge (for right swipe)
        // or if sidebar is open (for left swipe to close)
        const edgeThreshold = 110; // pixels from edge
        if (this.touchStartX <= edgeThreshold || this.sidebarVisible) {
            this.isSwipeGesture = true;
        }
    }

    handleTouchMove(e) {
        if (!this.isMobile() || !this.isSwipeGesture) return;

        const touch = e.touches[0];
        this.touchEndX = touch.clientX;
        this.touchEndY = touch.clientY;

        // Calculate swipe distance and angle
        const deltaX = this.touchEndX - this.touchStartX;
        const deltaY = this.touchEndY - this.touchStartY;
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
        const angle = Math.abs(Math.atan2(deltaY, deltaX) * 180 / Math.PI);

        // Check if this is a horizontal swipe
        const isHorizontalSwipe = angle < this.swipeAngleThreshold || angle > (180 - this.swipeAngleThreshold);

        // If it's a significant horizontal swipe, prevent default scrolling
        if (isHorizontalSwipe && distance > 20) {
            e.preventDefault();
        }
    }

    handleTouchEnd(e) {
        if (!this.isMobile() || !this.isSwipeGesture) return;

        const deltaX = this.touchEndX - this.touchStartX;
        const deltaY = this.touchEndY - this.touchStartY;
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
        const angle = Math.abs(Math.atan2(deltaY, deltaX) * 180 / Math.PI);

        // Check if this is a valid horizontal swipe
        const isHorizontalSwipe = angle < this.swipeAngleThreshold || angle > (180 - this.swipeAngleThreshold);

        if (isHorizontalSwipe && distance >= this.swipeThreshold) {
            if (deltaX > 0 && !this.sidebarVisible) {
                this.openSidebar();
            } else if (deltaX < 0 && this.sidebarVisible) {
                this.closeSidebar();
            }
        }

        // Reset swipe tracking
        this.isSwipeGesture = false;
    }

    addMouseSwipeEvents(targetElement) {
        // Add mouse events for desktop testing
        let mouseDown = false;
        let mouseStartX = 0;
        let mouseStartY = 0;

        targetElement.addEventListener('mousedown', (e) => {
            mouseDown = true;
            mouseStartX = e.clientX;
            mouseStartY = e.clientY;
        });

        targetElement.addEventListener('mousemove', (e) => {
            if (!mouseDown) return;

            const deltaX = e.clientX - mouseStartX;
            const deltaY = e.clientY - mouseStartY;
            const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

            // Simulate touch move
            this.touchEndX = e.clientX;
            this.touchEndY = e.clientY;
        });

        targetElement.addEventListener('mouseup', (e) => {
            if (!mouseDown) return;
            mouseDown = false;

            const deltaX = e.clientX - mouseStartX;
            const deltaY = e.clientY - mouseStartY;
            const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
            const angle = Math.abs(Math.atan2(deltaY, deltaX) * 180 / Math.PI);

            // Check if this is a valid horizontal swipe
            const isHorizontalSwipe = angle < this.swipeAngleThreshold || angle > (180 - this.swipeAngleThreshold);

            if (isHorizontalSwipe && distance >= this.swipeThreshold) {
                // Only allow swipes from left edge or when sidebar is open
                if ((mouseStartX <= 110 && deltaX > 0 && !this.sidebarVisible) ||
                    (deltaX < 0 && this.sidebarVisible)) {

                    if (deltaX > 0 && !this.sidebarVisible) {
                        this.openSidebar();
                    } else if (deltaX < 0 && this.sidebarVisible) {
                        this.closeSidebar();
                    }
                }
            }
        });
    }

    handleResize() {
        // Update viewport height for mobile browsers
        this.updateViewportHeight();

        // Close sidebar on mobile when switching to desktop
        if (!this.isMobile() && this.sidebarVisible) {
            this.sidebarBackdrop.classList.remove('show');
            document.body.style.overflow = '';
            this.mainContent.classList.remove('sidebar-hidden');
        }

        // Adjust sidebar behavior based on screen size
        if (this.isMobile()) {
            this.sidebar.classList.remove('hidden');
            this.mainContent.classList.remove('sidebar-hidden');
            if (!this.sidebarVisible) {
                this.sidebar.classList.add('hidden');
            }
        } else {
            this.sidebar.classList.remove('open');
            if (!this.sidebarVisible) {
                this.mainContent.classList.add('sidebar-hidden');
            }
        }
    }

    setupViewportHandler() {
        // Set initial viewport height
        this.updateViewportHeight();

        // Update on resize and orientation change
        window.addEventListener('resize', () => this.updateViewportHeight());
        window.addEventListener('orientationchange', () => {
            // Delay to allow browser to update viewport
            setTimeout(() => this.updateViewportHeight(), 100);
        });

        // Handle visual viewport changes (for mobile browsers)
        if (window.visualViewport) {
            window.visualViewport.addEventListener('resize', () => this.updateViewportHeight());
        }
    }

    updateViewportHeight() {
        // Get the actual viewport height
        let height = window.innerHeight;

        // For browsers that support visual viewport (better for mobile)
        if (window.visualViewport) {
            height = window.visualViewport.height;
        }

        // Special handling for Samsung Browser and other mobile browsers
        if (this.isMobile()) {
            // Use the larger of the two values to prevent content cutoff
            height = Math.max(window.innerHeight, window.screen.height);

            // For Samsung Browser specifically
            if (navigator.userAgent.includes('SamsungBrowser')) {
                height = window.screen.height;
            }

            // For Chrome on Android
            if (navigator.userAgent.includes('Chrome') && navigator.userAgent.includes('Mobile')) {
                height = window.innerHeight;
            }
        }

        const vh = height * 0.01;

        // Update CSS custom properties
        document.documentElement.style.setProperty('--vh', `${vh}px`);
        document.documentElement.style.setProperty('--app-height', `${height}px`);

        // Debug log for mobile browsers (remove in production)
        if (this.isMobile() && window.console) {
            console.log(`Viewport updated: ${height}px (inner: ${window.innerHeight}, screen: ${window.screen.height})`);
        }
    }

    async createNewChat() {
        // Check if there's already an empty "New Chat" that we can reuse
        const existingNewChat = Array.from(this.chats.values()).find(chat =>
            chat.title === 'New Chat' && chat.messages.length === 0
        );

        if (existingNewChat) {
            // Switch to the existing empty "New Chat" instead of creating a new one
            await this.switchToChat(existingNewChat.id);
            return;
        }

        let chatId, chat;

        if (this.useDatabase) {
            try {
                // Get current user ID
                const userId = await this.getCurrentUserId();

                // Create conversation in database with user ID
                const dbConversation = await this.supabaseService.createConversation('New Chat', userId);
                chatId = dbConversation.id;
                chat = {
                    id: chatId,
                    title: dbConversation.title,
                    messages: [],
                    createdAt: new Date(dbConversation.created_at),
                    updatedAt: new Date(dbConversation.updated_at),
                    isFromDatabase: true
                };
            } catch (error) {
                console.error('Failed to create conversation in database:', error);
                // Fallback to local storage
                this.useDatabase = false;
            }
        }

        if (!this.useDatabase) {
            // Fallback to local chat creation
            chatId = 'chat_' + Date.now();
            chat = {
                id: chatId,
                title: 'New Chat',
                messages: [],
                createdAt: new Date(),
                isFromDatabase: false
            };
        }

        this.chats.set(chatId, chat);
        this.currentChatId = chatId;
        this.resetScrollState(); // Reset scroll behavior for new chat

        // Clear any pending documents when creating a new chat
        this.clearPendingDocuments();

        // Clear requested agents for rotation
        this.requestedAgents = [];

        this.updateChatHistory();
        this.renderChat();
    }

    async loadConversationsFromDatabase() {
        if (!this.useDatabase) {
            // If no database, create a default chat
            this.createNewChat();
            return;
        }

        try {
            console.log('Loading conversations from database...');

            // Get current user ID to filter conversations
            const userId = await this.getCurrentUserId();
            const conversations = await this.supabaseService.getAllConversations(userId);

            if (conversations.length === 0) {
                // No conversations found, create a new one
                await this.createNewChat();
                return;
            }

            // Load conversations into memory
            for (const conv of conversations) {
                const chat = {
                    id: conv.id,
                    title: conv.title,
                    messages: [], // Messages will be loaded when chat is selected
                    createdAt: new Date(conv.created_at),
                    updatedAt: new Date(conv.updated_at),
                    isFromDatabase: true
                };
                this.chats.set(conv.id, chat);
            }

            // Set the most recent conversation as current
            const mostRecent = conversations[0]; // Already sorted by updated_at desc
            this.currentChatId = mostRecent.id;

            // Load messages for the current conversation
            await this.loadMessagesForChat(this.currentChatId);

            this.updateChatHistory();
            this.renderChat();

            console.log(`Loaded ${conversations.length} conversations from database`);
        } catch (error) {
            console.error('Failed to load conversations from database:', error);
            // Fallback to local mode
            this.useDatabase = false;
            this.createNewChat();
        }
    }

    async loadMessagesForChat(chatId) {
        if (!this.useDatabase) return;

        try {
            const chat = this.chats.get(chatId);
            if (!chat || !chat.isFromDatabase) return;

            const messages = await this.supabaseService.getMessages(chatId);

            // Convert database messages to app format
            chat.messages = messages.map(msg => {
                const message = {
                    role: msg.role,
                    content: msg.content,
                    timestamp: new Date(msg.created_at),
                    id: msg.id
                };

                // Restore memory acknowledgments from metadata if they exist
                if (msg.metadata && msg.metadata.memoryAcknowledgments) {
                    message.memoryAcknowledgments = msg.metadata.memoryAcknowledgments;
                }

                // Restore document info from metadata if it exists
                if (msg.metadata && msg.metadata.documentInfo) {
                    message.documentInfo = msg.metadata.documentInfo;
                }

                return message;
            });

            console.log(`Loaded ${messages.length} messages for conversation ${chatId}`);
        } catch (error) {
            console.error('Failed to load messages for chat:', error);
        }
    }

    async switchToChat(chatId) {
        if (this.chats.has(chatId)) {
            this.currentChatId = chatId;

            // Load messages from database if needed
            const chat = this.chats.get(chatId);
            if (chat && chat.isFromDatabase && chat.messages.length === 0) {
                await this.loadMessagesForChat(chatId);
            }

            this.resetScrollState(); // Reset scroll behavior when switching chats

            // Clear any pending documents when switching chats
            this.clearPendingDocuments();

            this.updateChatHistory();
            this.renderChat();
        }
    }

    clearCurrentChat() {
        if (this.currentChatId && this.chats.has(this.currentChatId)) {
            const chat = this.chats.get(this.currentChatId);
            chat.messages = [];
            chat.title = 'New Chat';
            this.resetScrollState(); // Reset scroll behavior when clearing chat

            // Clear any pending documents when clearing chat
            this.clearPendingDocuments();

            // Clear requested agents for rotation
            this.requestedAgents = [];

            this.updateChatHistory();
            this.renderChat();
        }
    }

    toggleMultiAgentMode() {
        this.useMultiAgent = !this.useMultiAgent;

        // Update button appearance
        if (this.useMultiAgent) {
            this.multiAgentToggle.classList.add('active');
            this.multiAgentToggle.title = 'AI Team Mode: ON - Complex requests handled by specialized AI agents working together';
        } else {
            this.multiAgentToggle.classList.remove('active');
            this.multiAgentToggle.title = 'AI Team Mode: OFF - Standard single-agent responses';
        }

        // Show/hide the agent discussion panel
        const agentDiscussionPanel = document.getElementById('agentDiscussionPanel');
        if (agentDiscussionPanel) {
            if (this.useMultiAgent) {
                agentDiscussionPanel.style.display = 'block';
                // Show the thinking status box when multi-agent mode is enabled
                const thinkingStatus = document.getElementById('agentThinkingStatus');
                if (thinkingStatus) {
                    thinkingStatus.classList.add('active');
                }
                // Start loading text rotation to show the system is ready
                this.startLoadingTextRotation();
                // Stop it after a few cycles to show it's ready
                setTimeout(() => {
                    this.stopLoadingTextRotation();
                    const statusTextElement = document.getElementById('discussionStatusText');
                    if (statusTextElement) {
                        statusTextElement.textContent = "🤖 AI Team Mode Ready - Send a message to activate!";
                    }
                }, 7500); // Show 3 cycles (2.5s each)
            } else {
                agentDiscussionPanel.style.display = 'none';
                // Hide the thinking status box when multi-agent mode is disabled
                const thinkingStatus = document.getElementById('agentThinkingStatus');
                if (thinkingStatus) {
                    thinkingStatus.classList.remove('active');
                }
                // Stop any running loading text rotation
                this.stopLoadingTextRotation();
            }
        }

        // Save preference to localStorage
        localStorage.setItem('useMultiAgent', this.useMultiAgent.toString());

        // Notification removed for cleaner UX - mode change is visible via button state
    }

    // Start dynamic loading text rotation for multi-agent processing
    startLoadingTextRotation() {
        // Wait a bit for DOM to be ready if needed
        setTimeout(() => {
            // Try both the main status text and the thinking text
            const statusTextElement = document.getElementById('discussionStatusText');
            const thinkingTextElement = document.getElementById('thinkingText');

            console.log('Starting loading text rotation');
            console.log('Status text element found:', !!statusTextElement);
            console.log('Thinking text element found:', !!thinkingTextElement);

            // Use whichever element is available
            const targetElement = statusTextElement || thinkingTextElement;

            if (!targetElement) {
                console.warn('No suitable text element found for rotation');
                return;
            }

            // Clear any existing interval
            this.stopLoadingTextRotation();

            // Start with the first message
            this.currentLoadingMessageIndex = 0;
            targetElement.textContent = this.multiAgentLoadingMessages[this.currentLoadingMessageIndex];
            console.log('Set initial message:', this.multiAgentLoadingMessages[this.currentLoadingMessageIndex]);

            // Set up rotation interval (change message every 2.5 seconds)
            this.loadingMessageInterval = setInterval(() => {
                // Re-find the element each time in case DOM changes
                const currentStatusElement = document.getElementById('discussionStatusText');
                const currentThinkingElement = document.getElementById('thinkingText');
                const currentTarget = currentStatusElement || currentThinkingElement;

                if (currentTarget) {
                    this.currentLoadingMessageIndex = (this.currentLoadingMessageIndex + 1) % this.multiAgentLoadingMessages.length;
                    currentTarget.textContent = this.multiAgentLoadingMessages[this.currentLoadingMessageIndex];
                    console.log('Rotated to message:', this.multiAgentLoadingMessages[this.currentLoadingMessageIndex]);
                } else {
                    console.warn('Target element disappeared, stopping rotation');
                    this.stopLoadingTextRotation();
                }
            }, 2500);
        }, 100);
    }

    // Stop dynamic loading text rotation
    stopLoadingTextRotation() {
        if (this.loadingMessageInterval) {
            clearInterval(this.loadingMessageInterval);
            this.loadingMessageInterval = null;
            console.log('Stopped loading text rotation');
        }
    }

    // Test function to manually trigger rotation (for debugging)
    testLoadingRotation() {
        console.log('Testing loading rotation...');
        const panel = document.getElementById('agentDiscussionPanel');
        const statusText = document.getElementById('discussionStatusText');
        const thinkingText = document.getElementById('thinkingText');
        const thinkingStatus = document.getElementById('agentThinkingStatus');

        console.log('Panel found:', !!panel, 'Panel display:', panel?.style.display);
        console.log('Status text found:', !!statusText, 'Current text:', statusText?.textContent);
        console.log('Thinking text found:', !!thinkingText, 'Current text:', thinkingText?.textContent);
        console.log('Thinking status found:', !!thinkingStatus);

        if (panel && panel.style.display === 'none') {
            panel.style.display = 'block';
            console.log('Made panel visible');
        }

        if (thinkingStatus && !thinkingStatus.classList.contains('active')) {
            thinkingStatus.classList.add('active');
            console.log('Activated thinking status');
        }

        this.startLoadingTextRotation();
    }

    // Load multi-agent preference from localStorage
    loadMultiAgentPreference() {
        const saved = localStorage.getItem('useMultiAgent');
        if (saved !== null) {
            this.useMultiAgent = saved === 'true';
            if (this.multiAgentToggle) {
                if (this.useMultiAgent) {
                    this.multiAgentToggle.classList.add('active');
                    this.multiAgentToggle.title = 'AI Team Mode: ON - Complex requests handled by specialized AI agents working together';
                } else {
                    this.multiAgentToggle.classList.remove('active');
                    this.multiAgentToggle.title = 'AI Team Mode: OFF - Standard single-agent responses';
                }
            }

            // Show/hide the agent discussion panel based on saved preference
            const agentDiscussionPanel = document.getElementById('agentDiscussionPanel');
            if (agentDiscussionPanel) {
                if (this.useMultiAgent) {
                    agentDiscussionPanel.style.display = 'block';
                    // Show the thinking status box when multi-agent mode is enabled
                    const thinkingStatus = document.getElementById('agentThinkingStatus');
                    if (thinkingStatus) {
                        thinkingStatus.classList.add('active');
                    }
                    // Set ready message for loaded multi-agent mode
                    const statusTextElement = document.getElementById('discussionStatusText');
                    if (statusTextElement) {
                        statusTextElement.textContent = "🤖 AI Team Mode Ready - Send a message to activate!";
                    }
                } else {
                    agentDiscussionPanel.style.display = 'none';
                }
            }
        }
    }

    async deleteChatWithConfirmation(chatId) {
        const chat = this.chats.get(chatId);
        if (!chat) return;

        // Show custom confirmation dialog
        const confirmed = await this.showCustomConfirmDialog({
            title: 'Delete Chat',
            message: `Are you sure you want to delete "${chat.title}"?`,
            description: 'This action cannot be undone. All messages in this conversation will be permanently deleted.',
            confirmText: 'Delete Chat',
            cancelText: 'Cancel',
            type: 'danger'
        });

        if (!confirmed) return;

        try {
            // Delete from database if it's a database chat
            if (this.useDatabase && chat.isFromDatabase) {
                await this.supabaseService.deleteConversation(chatId);
                console.log('Chat deleted from database:', chatId);
            }

            // Remove from local memory
            this.chats.delete(chatId);

            // If we deleted the current chat, switch to another chat or create a new one
            if (this.currentChatId === chatId) {
                // Clear any pending documents when deleting current chat
                this.clearPendingDocuments();

                const remainingChats = Array.from(this.chats.values());
                if (remainingChats.length > 0) {
                    // Switch to the most recent chat
                    const mostRecent = remainingChats.sort((a, b) => b.createdAt - a.createdAt)[0];
                    await this.switchToChat(mostRecent.id);
                } else {
                    // No chats left, create a new one
                    await this.createNewChat();
                }
            } else {
                // Just update the chat history
                this.updateChatHistory();
            }

            // Close any open dropdown menus
            document.querySelectorAll('.chat-dropdown-menu.show').forEach(menu => {
                menu.classList.remove('show');
            });

        } catch (error) {
            console.error('Failed to delete chat:', error);
            alert('Failed to delete chat. Please try again.');
        }
    }

    updateChatHistory() {
        this.chatHistory.innerHTML = '';

        const sortedChats = Array.from(this.chats.values())
            .sort((a, b) => b.createdAt - a.createdAt);

        sortedChats.forEach(chat => {
            const chatItem = document.createElement('div');
            chatItem.className = `chat-item ${chat.id === this.currentChatId ? 'active' : ''}`;
            chatItem.dataset.chatId = chat.id;

            // Create chat title element
            const chatTitle = document.createElement('span');
            chatTitle.className = 'chat-title';
            chatTitle.textContent = chat.title;

            // Create menu button
            const menuButton = document.createElement('button');
            menuButton.className = 'chat-menu-button';
            menuButton.innerHTML = '<i class="fas fa-ellipsis-vertical"></i>';
            menuButton.setAttribute('aria-label', 'Chat options');

            // Create dropdown menu
            const dropdownMenu = document.createElement('div');
            dropdownMenu.className = 'chat-dropdown-menu';
            dropdownMenu.innerHTML = `
                <button class="dropdown-item delete-chat-btn" data-chat-id="${chat.id}">
                    <i class="fas fa-trash"></i>
                    Delete chat
                </button>
            `;

            chatItem.appendChild(chatTitle);
            chatItem.appendChild(menuButton);
            chatItem.appendChild(dropdownMenu);
            this.chatHistory.appendChild(chatItem);
        });
    }

    renderChat() {
        const chat = this.chats.get(this.currentChatId);
        if (!chat) return;

        if (chat.messages.length === 0) {
            this.showWelcomeMessage();
        } else {
            this.showChatMessages(chat.messages);
        }
    }

    showWelcomeMessage() {
        this.chatContainer.innerHTML = `
            <div class="welcome-message">
                <div class="welcome-icon">
                    <img src="robot-icon.png" alt="Robot Icon" class="robot-icon-img">
                </div>
                <h2>Hello! I'm Veritas ✨</h2>
                <p>Your intelligent AI companion created by Bradley. I'm here to help, learn about you, and provide thoughtful assistance with whatever you need. How can I help you today?</p>
                
                <div class="example-prompts">
                    <div class="prompt-card" data-prompt="Explain quantum computing in simple terms">
                        <i class="fas fa-atom"></i>
                        <span>Explain quantum computing</span>
                    </div>
                    <div class="prompt-card" data-prompt="Write a creative story about space exploration">
                        <i class="fas fa-rocket"></i>
                        <span>Write a creative story</span>
                    </div>
                    <div class="prompt-card" data-prompt="Help me plan a healthy meal for the week">
                        <i class="fas fa-utensils"></i>
                        <span>Plan a healthy meal</span>
                    </div>
                    <div class="prompt-card" data-prompt="Explain the basics of machine learning">
                        <i class="fas fa-brain"></i>
                        <span>Explain machine learning</span>
                    </div>
                </div>
            </div>
        `;
    }

    showChatMessages(messages) {
        this.chatContainer.innerHTML = '';
        
        messages.forEach(message => {
            this.addMessageToDOM(message);
        });

        this.scrollToBottom(true); // Force scroll when loading messages
    }

    addMessageToDOM(message) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${message.role}-message`;

        // Store original content for potential re-formatting
        messageDiv.dataset.originalContent = message.content;

        const avatar = document.createElement('div');
        avatar.className = `message-avatar ${message.role}-avatar`;
        avatar.innerHTML = message.role === 'user' ? '' : '<i class="fas fa-brain"></i>';

        const content = document.createElement('div');
        content.className = 'message-content';
        content.innerHTML = this.formatMessage(message.content);

        // Add copy functionality to code blocks
        this.addCodeBlockCopyButtons(content);

        messageDiv.appendChild(avatar);
        messageDiv.appendChild(content);

        // Add document information for user messages
        if (message.role === 'user' && message.documentInfo) {
            const documentDisplay = document.createElement('div');
            documentDisplay.className = 'document-analysis-display';
            documentDisplay.innerHTML = `
                <div class="document-analysis-header">
                    <i class="fas fa-file-alt"></i>
                    <span>Attached Documents (${message.documentInfo.count})</span>
                </div>
            `;
            messageDiv.appendChild(documentDisplay);
        }

        // Check if this message has any associated memory acknowledgments and restore them
        if (message.role === 'assistant' && message.memoryAcknowledgments) {
            message.memoryAcknowledgments.forEach(memoryContent => {
                const memoryAck = document.createElement('div');
                memoryAck.className = 'memory-acknowledgment';
                memoryAck.innerHTML = `
                    <i class="fas fa-brain"></i>
                    <span>Remembered: ${this.escapeHtml(memoryContent)}</span>
                `;
                messageDiv.appendChild(memoryAck);
            });
        }

        this.chatContainer.appendChild(messageDiv);
    }

    addCodeBlockCopyButtons(container) {
        const codeBlocks = container.querySelectorAll('pre code');
        codeBlocks.forEach((codeBlock) => {
            const pre = codeBlock.parentElement;
            const copyButton = document.createElement('button');
            copyButton.className = 'code-copy-btn';
            copyButton.innerHTML = '<i class="fas fa-copy"></i>';
            copyButton.title = 'Copy code';
            copyButton.setAttribute('aria-label', 'Copy code to clipboard');

            copyButton.addEventListener('click', async () => {
                try {
                    await navigator.clipboard.writeText(codeBlock.textContent);
                    copyButton.innerHTML = '<i class="fas fa-check"></i>';
                    copyButton.classList.add('success');
                    setTimeout(() => {
                        copyButton.innerHTML = '<i class="fas fa-copy"></i>';
                        copyButton.classList.remove('success');
                    }, 2000);
                } catch (err) {
                    console.error('Failed to copy code:', err);
                    // Fallback for older browsers
                    const textArea = document.createElement('textarea');
                    textArea.value = codeBlock.textContent;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);

                    copyButton.innerHTML = '<i class="fas fa-check"></i>';
                    copyButton.classList.add('success');
                    setTimeout(() => {
                        copyButton.innerHTML = '<i class="fas fa-copy"></i>';
                        copyButton.classList.remove('success');
                    }, 2000);
                }
            });

            pre.appendChild(copyButton);
        });
    }

    formatMessage(content) {
        // Enhanced markdown-like formatting with HTML support
        let formatted = content;

        // Handle code blocks first (before other formatting) with language detection
        formatted = formatted.replace(/```(\w+)?\n?([\s\S]*?)```/g, (_, language, code) => {
            const lang = language ? language.toLowerCase() : '';
            const langAttr = lang ? ` data-language="${lang}"` : '';
            // Escape HTML entities in code to prevent browser interpretation
            const escapedCode = this.escapeHtml(code.trim());
            // Apply syntax highlighting based on language
            const highlightedCode = this.applySyntaxHighlighting(escapedCode, lang);
            return `<pre${langAttr}><code>${highlightedCode}</code></pre>`;
        });

        // Handle headers with minimal spacing
        formatted = formatted.replace(/^### (.*$)/gm, '<h3>$1</h3>');
        formatted = formatted.replace(/^## (.*$)/gm, '<h2>$1</h2>');
        formatted = formatted.replace(/^# (.*$)/gm, '<h1>$1</h1>');

        // Handle bold and italic with proper spacing preservation
        // Bold: **text** - handle with word boundaries to preserve spacing
        formatted = formatted.replace(/\*\*([^*]+?)\*\*/g, '<strong>$1</strong>');

        // Single asterisk: *text* - convert to bold instead of italic
        // More robust approach: only match asterisks that form complete patterns
        // This prevents standalone asterisks from being consumed
        formatted = formatted.replace(/(?<!\*)\*([^*\n\r]+?)\*(?!\*)/g, (match, content, offset, string) => {
            // Additional validation: ensure this looks like intentional formatting
            // Check that content is not just whitespace and doesn't look like a typo
            const trimmedContent = content.trim();
            if (trimmedContent.length === 0) {
                return match; // Don't format empty content
            }

            // Check if this might be a standalone asterisk (like a typo or emphasis)
            // Look for patterns like "word* word" which are likely not intended as formatting
            const beforeChar = offset > 0 ? string[offset - 1] : '';
            const afterMatch = string.substring(offset + match.length, offset + match.length + 1);

            // If there's a letter immediately before the opening * and a space after closing *,
            // this might be a standalone asterisk, not formatting
            if (/\w/.test(beforeChar) && /\s/.test(afterMatch)) {
                return match; // Preserve as-is, likely not intended formatting
            }

            return `<strong>${content}</strong>`;
        });

        // Handle inline code with HTML escaping
        formatted = formatted.replace(/`(.*?)`/g, (_, code) => {
            const escapedCode = this.escapeHtml(code);
            return `<code>${escapedCode}</code>`;
        });

        // Handle markdown tables
        formatted = this.formatTables(formatted);

        // Handle bullet points and numbered lists
        formatted = this.formatLists(formatted);

        // Handle line breaks
        formatted = formatted.replace(/\n/g, '<br>');

        // Clean up excess breaks
        formatted = formatted.replace(/^<br>+/, ''); // Remove breaks at start
        formatted = formatted.replace(/<br>+$/, ''); // Remove breaks at end
        formatted = formatted.replace(/(<br>){2,}/g, '</p><p>'); // Convert double breaks to paragraph breaks

        // Reduce spacing before lists - remove ALL breaks before <ul> and <ol>
        formatted = formatted.replace(/(<br>\s*)+<ul>/g, '<ul>');
        formatted = formatted.replace(/(<br>\s*)+<ol>/g, '<ol>');

        // Reduce spacing around tables - remove excess breaks before and after tables
        formatted = formatted.replace(/(<br>\s*)+<table>/g, '<table>');
        formatted = formatted.replace(/<\/table>(<br>\s*)+/g, '</table><br>');

        // Reduce spacing after headers - remove extra breaks after headers and before lists
        formatted = formatted.replace(/<\/h1>(<br>\s*)+<ul>/g, '</h1><ul>');
        formatted = formatted.replace(/<\/h1>(<br>\s*)+<ol>/g, '</h1><ol>');
        formatted = formatted.replace(/<\/h2>(<br>\s*)+<ul>/g, '</h2><ul>');
        formatted = formatted.replace(/<\/h2>(<br>\s*)+<ol>/g, '</h2><ol>');
        formatted = formatted.replace(/<\/h3>(<br>\s*)+<ul>/g, '</h3><ul>');
        formatted = formatted.replace(/<\/h3>(<br>\s*)+<ol>/g, '</h3><ol>');

        // Reduce spacing after headers - remove extra breaks after headers
        formatted = formatted.replace(/<\/h1>(<br>\s*){2,}/g, '</h1><br>');
        formatted = formatted.replace(/<\/h2>(<br>\s*){2,}/g, '</h2><br>');
        formatted = formatted.replace(/<\/h3>(<br>\s*){2,}/g, '</h3><br>');

        // Reduce spacing after lists - remove extra breaks after bullet points and numbered lists
        formatted = formatted.replace(/<\/ul>(<br>\s*)+/g, '</ul>');
        formatted = formatted.replace(/<\/ol>(<br>\s*)+/g, '</ol>');

        // Wrap content in paragraphs for better spacing (avoid wrapping if already has block elements)
        if (!formatted.includes('<h1>') && !formatted.includes('<h2>') && !formatted.includes('<h3>') &&
            !formatted.includes('<ul>') && !formatted.includes('<ol>') && !formatted.includes('<pre>') &&
            !formatted.includes('<table>')) {
            formatted = '<p>' + formatted + '</p>';
        }

        return formatted;
    }

    escapeHtml(text) {
        // Escape HTML entities to prevent browser interpretation
        if (typeof text !== 'string') {
            return '';
        }
        // Use a more explicit approach to ensure proper escaping
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;');
    }

    applySyntaxHighlighting(code, language) {
        // Apply basic syntax highlighting based on language
        switch (language) {
            case 'css':
                return this.highlightCss(code);
            case 'javascript':
            case 'js':
                return this.highlightJavaScript(code);
            case 'python':
                return this.highlightPython(code);
            case 'json':
                return this.highlightJson(code);
            default:
                return code; // Return as-is for unsupported languages
        }
    }



    highlightCss(code) {
        return code
            // Selectors
            .replace(/^([.#]?[a-zA-Z][a-zA-Z0-9-_]*)\s*{/gm, '<span class="css-selector">$1</span> {')
            // Properties
            .replace(/(\s+)([a-zA-Z-]+)(\s*:)/g, '$1<span class="css-property">$2</span>$3')
            // Values
            .replace(/(:\s*)([^;]+)(;)/g, '$1<span class="css-value">$2</span>$3')
            // Comments
            .replace(/(\/\*[\s\S]*?\*\/)/g, '<span class="css-comment">$1</span>');
    }

    highlightJavaScript(code) {
        return code
            // Keywords
            .replace(/\b(function|var|let|const|if|else|for|while|return|class|extends|import|export|from|default|async|await|try|catch|finally|throw|new|this|super|static|get|set|typeof|instanceof|in|of|delete|void|null|undefined|true|false)\b/g,
                '<span class="js-keyword">$1</span>')
            // Strings
            .replace(/(&quot;[^&]*&quot;|&#x27;[^&#]*&#x27;|`[^`]*`)/g, '<span class="js-string">$1</span>')
            // Numbers
            .replace(/\b(\d+\.?\d*)\b/g, '<span class="js-number">$1</span>')
            // Comments
            .replace(/(\/\/.*$|\/\*[\s\S]*?\*\/)/gm, '<span class="js-comment">$1</span>');
    }

    highlightPython(code) {
        return code
            // Keywords
            .replace(/\b(def|class|if|elif|else|for|while|try|except|finally|with|as|import|from|return|yield|lambda|and|or|not|in|is|None|True|False|pass|break|continue|global|nonlocal|assert|del|raise|async|await)\b/g,
                '<span class="py-keyword">$1</span>')
            // Strings
            .replace(/(&quot;[^&]*&quot;|&#x27;[^&#]*&#x27;|&quot;&quot;&quot;[\s\S]*?&quot;&quot;&quot;|&#x27;&#x27;&#x27;[\s\S]*?&#x27;&#x27;&#x27;)/g, '<span class="py-string">$1</span>')
            // Numbers
            .replace(/\b(\d+\.?\d*)\b/g, '<span class="py-number">$1</span>')
            // Comments
            .replace(/(#.*$)/gm, '<span class="py-comment">$1</span>');
    }

    highlightJson(code) {
        return code
            // Strings (keys and values)
            .replace(/(&quot;[^&]*&quot;)(\s*:)/g, '<span class="json-key">$1</span>$2')
            .replace(/(:\s*)(&quot;[^&]*&quot;)/g, '$1<span class="json-string">$2</span>')
            // Numbers
            .replace(/(:\s*)(\d+\.?\d*)/g, '$1<span class="json-number">$2</span>')
            // Booleans and null
            .replace(/\b(true|false|null)\b/g, '<span class="json-literal">$1</span>');
    }

    formatTables(content) {
        // Split content into lines for processing
        const lines = content.split('\n');
        const result = [];
        let i = 0;

        while (i < lines.length) {
            const line = lines[i].trim();

            // Check if this line looks like a table header (contains |)
            if (line.includes('|') && line.split('|').length >= 3) {
                // Look ahead to see if the next line is a separator (contains dashes and |)
                const nextLine = i + 1 < lines.length ? lines[i + 1].trim() : '';
                const isSeparatorLine = nextLine.includes('|') && nextLine.includes('-');

                if (isSeparatorLine) {
                    // We found a table! Parse it
                    const tableResult = this.parseMarkdownTable(lines, i);
                    result.push(tableResult.html);
                    i = tableResult.nextIndex;
                    continue;
                }
            }

            // Not a table line, add as-is
            result.push(lines[i]);
            i++;
        }

        return result.join('\n');
    }

    parseMarkdownTable(lines, startIndex) {
        const tableLines = [];
        let currentIndex = startIndex;

        // Collect all table lines (lines that contain |)
        while (currentIndex < lines.length) {
            const line = lines[currentIndex].trim();
            if (line.includes('|')) {
                tableLines.push(line);
                currentIndex++;
            } else {
                break;
            }
        }

        if (tableLines.length < 2) {
            // Not a valid table, return original lines
            return {
                html: lines.slice(startIndex, currentIndex).join('\n'),
                nextIndex: currentIndex
            };
        }

        // Parse header row
        const headerRow = tableLines[0];
        const headers = this.parseTableRow(headerRow);

        // Skip separator row (index 1)
        const dataRows = tableLines.slice(2);

        // Build HTML table (compact format to avoid spacing issues)
        let html = '<table>';

        // Add header
        html += '<thead><tr>';
        headers.forEach(header => {
            html += `<th>${this.formatTableCellContent(header.trim())}</th>`;
        });
        html += '</tr></thead>';

        // Add body
        if (dataRows.length > 0) {
            html += '<tbody>';
            dataRows.forEach(row => {
                const cells = this.parseTableRow(row);
                html += '<tr>';
                cells.forEach(cell => {
                    html += `<td>${this.formatTableCellContent(cell.trim())}</td>`;
                });
                html += '</tr>';
            });
            html += '</tbody>';
        }

        html += '</table>';

        return {
            html: html,
            nextIndex: currentIndex
        };
    }

    parseTableRow(row) {
        // Split by | and remove empty first/last elements if they exist
        let cells = row.split('|');

        // Remove empty cells at the beginning and end (from leading/trailing |)
        if (cells[0].trim() === '') {
            cells = cells.slice(1);
        }
        if (cells[cells.length - 1].trim() === '') {
            cells = cells.slice(0, -1);
        }

        return cells;
    }

    formatTableCellContent(content) {
        // Apply basic formatting to table cell content without escaping HTML
        let formatted = content;

        // Handle bold formatting: **text** -> <strong>text</strong>
        formatted = formatted.replace(/\*\*([^*]+?)\*\*/g, '<strong>$1</strong>');

        // Handle single asterisk formatting: *text* -> <strong>text</strong> (bold instead of italic)
        // Use the same robust logic as the main formatMessage function
        formatted = formatted.replace(/(?<!\*)\*([^*\n\r]+?)\*(?!\*)/g, (match, content, offset, string) => {
            const trimmedContent = content.trim();
            if (trimmedContent.length === 0) {
                return match;
            }

            const beforeChar = offset > 0 ? string[offset - 1] : '';
            const afterMatch = string.substring(offset + match.length, offset + match.length + 1);

            if (/\w/.test(beforeChar) && /\s/.test(afterMatch)) {
                return match;
            }

            return `<strong>${content}</strong>`;
        });

        // Handle inline code: `text` -> <code>text</code>
        formatted = formatted.replace(/`(.*?)`/g, (_, code) => {
            const escapedCode = this.escapeHtml(code);
            return `<code>${escapedCode}</code>`;
        });

        // For any remaining content that's not already formatted, escape HTML entities
        // but preserve the formatting tags we just added
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = formatted;

        // If the innerHTML is the same as what we put in, it means our formatting was preserved
        // If not, we need to escape the original content
        if (tempDiv.innerHTML !== formatted) {
            // There was unescaped HTML, so escape everything except our formatting
            formatted = content
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#39;');

            // Re-apply our formatting
            formatted = formatted.replace(/\*\*([^*]+?)\*\*/g, '<strong>$1</strong>');
            formatted = formatted.replace(/(?<!\*)\*([^*\n\r]+?)\*(?!\*)/g, (match, content, offset, string) => {
                const trimmedContent = content.trim();
                if (trimmedContent.length === 0) {
                    return match;
                }

                const beforeChar = offset > 0 ? string[offset - 1] : '';
                const afterMatch = string.substring(offset + match.length, offset + match.length + 1);

                if (/\w/.test(beforeChar) && /\s/.test(afterMatch)) {
                    return match;
                }

                return `<strong>${content}</strong>`;
            });
            formatted = formatted.replace(/`(.*?)`/g, (_, code) => {
                return `<code>${code}</code>`;
            });
        }

        return formatted;
    }

    formatLists(content) {
        const lines = content.split('\n');
        const result = [];
        let inUnorderedList = false;
        let inOrderedList = false;
        let listLevel = 0;

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();

            // Skip empty lines within lists to avoid breaking them
            if (line === '' && (inUnorderedList || inOrderedList)) {
                continue;
            }

            // Check for bullet points (-, *, +)
            const bulletMatch = line.match(/^(\s*)([-*+])\s+(.+)$/);
            // Check for numbered lists (1., 2., etc.)
            const numberedMatch = line.match(/^(\s*)(\d+\.)\s+(.+)$/);

            if (bulletMatch) {
                const [, indent, , text] = bulletMatch;
                const currentLevel = Math.floor(indent.length / 2);

                if (!inUnorderedList || currentLevel !== listLevel) {
                    if (inOrderedList) {
                        result.push('</ol>');
                        inOrderedList = false;
                    }
                    if (!inUnorderedList) {
                        result.push('<ul>');
                        inUnorderedList = true;
                    }
                    listLevel = currentLevel;
                }

                // Check if this list item should have no bullet (ends with colon and is short)
                const shouldHidebullet = this.shouldHideBulletPoint(text);
                const liClass = shouldHidebullet ? ' class="no-bullet"' : '';
                result.push(`<li${liClass}>${text}</li>`);
            } else if (numberedMatch) {
                const [, indent, , text] = numberedMatch;
                const currentLevel = Math.floor(indent.length / 2);

                if (!inOrderedList || currentLevel !== listLevel) {
                    if (inUnorderedList) {
                        result.push('</ul>');
                        inUnorderedList = false;
                    }
                    if (!inOrderedList) {
                        result.push('<ol>');
                        inOrderedList = true;
                    }
                    listLevel = currentLevel;
                }

                // Check if this list item should have no bullet (ends with colon and is short)
                const shouldHideNumbering = this.shouldHideBulletPoint(text);
                const liClass = shouldHideNumbering ? ' class="no-bullet"' : '';
                result.push(`<li${liClass}>${text}</li>`);
            } else {
                // Close any open lists when we encounter a non-list line
                if (inUnorderedList) {
                    result.push('</ul>');
                    inUnorderedList = false;
                }
                if (inOrderedList) {
                    result.push('</ol>');
                    inOrderedList = false;
                }
                listLevel = 0;

                // Add the regular line (preserve original line with whitespace for non-list content)
                result.push(lines[i]);
            }
        }

        // Close any remaining open lists
        if (inUnorderedList) {
            result.push('</ul>');
        }
        if (inOrderedList) {
            result.push('</ol>');
        }

        return result.join('\n');
    }

    shouldHideBulletPoint(text) {
        // Check if this list item should have its bullet/numbering hidden
        // Criteria: ends with colon and appears to be a header/section label

        const trimmedText = text.trim();

        // Must end with a colon (check for various colon characters)
        if (!trimmedText.match(/:$/)) {
            return false;
        }

        // Remove the colon for analysis
        const textWithoutColon = trimmedText.replace(/:$/, '').trim();

        // Hide bullet if it appears to be a section header or label:
        // 1. Text without colon is 80 characters or less, AND
        // 2. Contains 10 words or fewer, AND
        // 3. Doesn't contain sentence-ending punctuation (., !, ?) AND
        // 4. Doesn't contain common sentence indicators
        const wordCount = textWithoutColon.split(/\s+/).filter(word => word.length > 0).length;
        const hasEndPunctuation = /[.!?]/.test(textWithoutColon);
        const hasSentenceIndicators = /\b(because|however|therefore|although|since|while|when|if|but|and|or|so|then|also|additionally|furthermore|moreover|nevertheless|nonetheless)\b/i.test(textWithoutColon);

        return textWithoutColon.length <= 80 &&
               wordCount <= 10 &&
               !hasEndPunctuation &&
               !hasSentenceIndicators;
    }

    async sendMessage() {
        // Prevent duplicate calls while already processing
        if (this.isSendingMessage) {
            console.warn('Duplicate sendMessage() call prevented - message already being sent');
            return;
        }

        // Ensure elements are still available and functional
        if (!this.messageInput || !this.sendBtn) {
            console.warn('Input elements not available, re-initializing...');
            this.initializeElements();
            return;
        }

        let message = this.messageInput.value.trim();
        if (!message || this.isTyping) return;

        // Set flag to prevent duplicate calls
        this.isSendingMessage = true;

        const chat = this.chats.get(this.currentChatId);
        if (!chat) return;

        // Additional safety check - ensure send button is not disabled
        if (this.sendBtn.disabled && !this.isTyping) {
            this.handleInputChange(); // Refresh button state
            if (this.sendBtn.disabled) return;
        }

        // Combine user message with pending documents if any
        let finalMessage = message;
        let documentInfo = null;
        if (this.pendingDocuments) {
            const { extractedTexts } = this.pendingDocuments;

            // Create document summary for display
            documentInfo = {
                count: extractedTexts.length,
                summary: extractedTexts.map(doc => {
                    const sizeStr = doc.size ? ` (${(doc.size / 1024).toFixed(1)}KB)` : '';
                    const urlStr = doc.fileUrl ? ' 🔗' : '';
                    return `📄 **${doc.filename}** (${doc.type})${sizeStr}${urlStr}`;
                }).join('\n')
            };

            // Combine all document content for AI
            const combinedContent = extractedTexts.map(doc =>
                `=== ${doc.filename} (${doc.type}) ===\n${doc.content}\n`
            ).join('\n');

            // Create the enhanced message for AI
            finalMessage = `${message}

I've uploaded ${extractedTexts.length} document(s) for analysis:

Here's the full content of the documents:

${combinedContent}`;

            // Clear pending documents after using them for this message
            this.clearPendingDocuments();
        }

        // Add user message (store original user message, but send enhanced version to AI)
        const userMessage = {
            role: 'user',
            content: message,
            timestamp: new Date(),
            documentInfo: documentInfo // Include document info for display
        };
        chat.messages.push(userMessage);

        // Save user message to database
        if (this.useDatabase && chat.isFromDatabase) {
            try {
                // Prepare metadata including document info and memory acknowledgments
                const metadata = {};
                if (userMessage.memoryAcknowledgments && userMessage.memoryAcknowledgments.length > 0) {
                    metadata.memoryAcknowledgments = userMessage.memoryAcknowledgments;
                }
                if (userMessage.documentInfo) {
                    metadata.documentInfo = userMessage.documentInfo;
                }

                const dbMessage = await this.supabaseService.addMessage(
                    chat.id,
                    userMessage.role,
                    userMessage.content,
                    metadata
                );
                userMessage.id = dbMessage.id;
            } catch (error) {
                console.error('Failed to save user message to database:', error);
            }
        }

        // Update chat title if it's the first message
        if (chat.messages.length === 1) {
            const newTitle = message.length > 30 ? message.substring(0, 30) + '...' : message;
            chat.title = newTitle;

            // Update title in database
            if (this.useDatabase && chat.isFromDatabase) {
                try {
                    await this.supabaseService.updateConversationTitle(chat.id, newTitle);
                } catch (error) {
                    console.error('Failed to update conversation title in database:', error);
                }
            }

            this.updateChatHistory();
        }

        // Preserve input height if it was manually expanded
        const currentHeight = parseInt(this.messageInput.style.height);
        if (currentHeight > 32) { // 32px is roughly the minimum height
            this.preservedInputHeight = currentHeight;
        }

        // Clear input and show user message
        this.messageInput.value = '';
        // Reset preserved height when message is sent
        this.preservedInputHeight = null;
        this.handleInputChange();
        this.autoResize(); // Reset textarea to original size
        this.showChatMessages(chat.messages);

        // Ensure we scroll to the very bottom after showing messages
        setTimeout(() => {
            this.scrollToBottom(true, true);
        }, 100);

        // Show typing indicator (brain icon only)
        this.showTypingIndicator();
        this.isTyping = true;

        // Determine whether to use multi-agent system
        const shouldUseMultiAgent = this.useMultiAgent || this.multiAgentSystem.shouldUseMultiAgent(finalMessage);

        // Generate AI response with conversation history (use finalMessage for AI, but original message for memory detection)
        try {
            let aiResponse;

            if (shouldUseMultiAgent) {
                // Use multi-agent system with progress tracking
                aiResponse = await this.generateMultiAgentResponse(finalMessage, chat.messages, message);
            } else {
                // Use standard single-agent response
                aiResponse = await this.generateAIResponse(finalMessage, chat.messages, message);
            }

            // Process the response to handle /title command
            const processedResponse = this.processAIResponse(aiResponse);

            const aiMessage = { role: 'assistant', content: processedResponse.content, timestamp: new Date() };

            this.hideTypingIndicator();

            // Update chat title if /title command was found AND chat doesn't already have a custom title
            if (processedResponse.title && this.shouldUpdateTitle(chat)) {
                const oldTitle = chat.title;
                chat.title = processedResponse.title;

                // Update title in database
                if (this.useDatabase && chat.isFromDatabase) {
                    try {
                        await this.supabaseService.updateConversationTitle(chat.id, processedResponse.title);
                    } catch (error) {
                        console.error('Failed to update conversation title in database:', error);
                    }
                }

                console.log(`Title updated from "${oldTitle}" to "${chat.title}"`);
                this.updateChatHistory();
            } else if (processedResponse.title && !this.shouldUpdateTitle(chat)) {
                console.log(`Title update skipped - chat already has custom title: "${chat.title}"`);
            }





            // Add the AI message to chat history but don't show it yet
            chat.messages.push(aiMessage);

            // Save AI message to database
            if (this.useDatabase && chat.isFromDatabase) {
                try {
                    // Prepare metadata including memory acknowledgments if they exist
                    const metadata = {};
                    if (aiMessage.memoryAcknowledgments && aiMessage.memoryAcknowledgments.length > 0) {
                        metadata.memoryAcknowledgments = aiMessage.memoryAcknowledgments;
                    }

                    const dbMessage = await this.supabaseService.addMessage(
                        chat.id,
                        aiMessage.role,
                        aiMessage.content,
                        metadata
                    );
                    aiMessage.id = dbMessage.id;
                } catch (error) {
                    console.error('Failed to save AI message to database:', error);
                }
            }

            // Show the message with typing animation
            await this.showTypingAnimation(aiMessage);

        } catch (error) {
            console.error('Error generating AI response:', error);
            this.hideTypingIndicator();
        } finally {
            // Always reset the sending flag, even if there was an error
            this.isSendingMessage = false;
        }

        this.isTyping = false;
    }

    showTypingIndicator() {
        const typingDiv = document.createElement('div');
        typingDiv.className = 'message ai-message typing-message';
        typingDiv.innerHTML = `
            <div class="message-avatar ai-avatar">
                <!-- Brain icon removed -->
            </div>
            <div class="message-content typing-indicator">
                <!-- Agent Thinking Status Box removed -->
            </div>
        `;
        this.chatContainer.appendChild(typingDiv);

        this.scrollToBottom(true, false); // Force scroll when showing typing indicator - instant
    }

    hideTypingIndicator() {
        const typingMessage = this.chatContainer.querySelector('.typing-message');
        if (typingMessage) {
            // Remove the typing message
            typingMessage.remove();
        }
    }

    async showTypingAnimation(message) {
        // Create the message container
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${message.role}-message`;

        const avatar = document.createElement('div');
        avatar.className = `message-avatar ${message.role}-avatar`;
        avatar.innerHTML = message.role === 'user' ? '' : '<i class="fas fa-brain"></i>';

        const content = document.createElement('div');
        content.className = 'message-content typing';

        // No cursor animation needed

        messageDiv.appendChild(avatar);
        messageDiv.appendChild(content);
        this.chatContainer.appendChild(messageDiv);

        // Scroll to bottom to show the new message
        this.scrollToBottom(true, false); // Force scroll when starting new message - instant

        // Get the formatted content
        const formattedContent = this.formatMessage(message.content);

        // Type the content with animation
        await this.typeContent(content, formattedContent);

        // Add copy functionality to code blocks after typing is complete
        this.addCodeBlockCopyButtons(content);

        // Remove typing class when done
        content.classList.remove('typing');
    }

    // Dynamic cursor functions removed - no longer needed

    async typeContent(element, htmlContent) {
        // Parse HTML content to handle formatting while typing
        const parser = new DOMParser();
        const doc = parser.parseFromString(`<div>${htmlContent}</div>`, 'text/html');
        const contentDiv = doc.querySelector('div');

        // Check for parsing errors
        const parserError = doc.querySelector('parsererror');
        if (parserError) {
            console.error('HTML parsing error:', parserError.textContent);
            // Fallback to plain text if parsing fails
            element.textContent = htmlContent.replace(/<[^>]*>/g, '');
            return;
        }

        // Pre-process the parsed content to clean up any remaining HTML entities in text nodes
        this.cleanTextNodesInElement(contentDiv);

        element.innerHTML = '';

        // Check if content contains code blocks for faster typing
        const hasCodeBlocks = contentDiv.querySelector('pre');

        // Fast typing speed (adjust these values to make it faster/slower)
        let TYPING_SPEED, PUNCTUATION_DELAY;

        if (hasCodeBlocks) {
            // Much faster typing for code blocks
            TYPING_SPEED = 0.1; // milliseconds per character (very fast for code)
            PUNCTUATION_DELAY = 0; // no extra delay for punctuation in code
        } else {
            // Normal typing speed for regular text
            TYPING_SPEED = 1; // milliseconds per character (lower = faster)
            PUNCTUATION_DELAY = 10; // extra delay for punctuation
        }

        try {
            await this.typeElement(element, contentDiv, TYPING_SPEED, PUNCTUATION_DELAY);
        } catch (error) {
            console.error('Error during typing animation:', error);
            // Fallback to instant display if typing fails
            element.innerHTML = htmlContent;
        }
    }

    cleanTextNodesInElement(element) {
        // Recursively clean all text nodes in the element to remove HTML entities
        if (!element || !element.childNodes) return;

        // Convert NodeList to array to avoid issues with live collections
        const nodes = Array.from(element.childNodes);

        for (const node of nodes) {
            if (node.nodeType === Node.TEXT_NODE) {
                // Clean HTML entities from text content
                let textContent = node.textContent || '';

                // Check for any HTML-like content
                if (textContent.includes('&') || textContent.includes('<') || textContent.includes('>')) {
                    try {
                        // First, strip any HTML tags to prevent injection
                        let cleanedText = textContent.replace(/<[^>]*>/g, '');

                        // Then decode HTML entities using a safer approach
                        // Only decode entities, don't parse HTML
                        cleanedText = cleanedText
                            .replace(/&lt;/g, '<')
                            .replace(/&gt;/g, '>')
                            .replace(/&quot;/g, '"')
                            .replace(/&#x27;/g, "'")
                            .replace(/&#39;/g, "'")
                            .replace(/&apos;/g, "'")
                            .replace(/&nbsp;/g, ' ')
                            .replace(/&amp;/g, '&'); // This should be last to avoid double-decoding

                        // Final safety check - ensure no HTML tags remain
                        cleanedText = cleanedText.replace(/<[^>]*>/g, '');

                        node.textContent = cleanedText;
                    } catch (error) {
                        console.warn('Error cleaning text node:', error);
                        // Fallback: just strip HTML tags and basic entity decode
                        let fallbackText = textContent.replace(/<[^>]*>/g, '');
                        fallbackText = fallbackText.replace(/&amp;/g, '&').replace(/&lt;/g, '<').replace(/&gt;/g, '>');
                        node.textContent = fallbackText;
                    }
                }
            } else if (node.nodeType === Node.ELEMENT_NODE) {
                // Recursively clean child elements
                this.cleanTextNodesInElement(node);
            }
        }
    }

    async typeElement(targetElement, sourceElement, typingSpeed, punctuationDelay) {
        if (!sourceElement || !sourceElement.childNodes) {
            console.error('Invalid sourceElement in typeElement:', sourceElement);
            return;
        }

        for (const node of sourceElement.childNodes) {
            try {
                if (node.nodeType === Node.TEXT_NODE) {
                    // Type text character by character
                    const textContent = node.textContent || '';

                    if (textContent) { // Only type non-empty text (including spaces)
                        await this.typeText(targetElement, textContent, typingSpeed, punctuationDelay);
                    }
                } else if (node.nodeType === Node.ELEMENT_NODE) {
                    // Create the HTML element and type its contents
                    const tagName = node.tagName ? node.tagName.toLowerCase() : 'div';
                    const newElement = document.createElement(tagName);

                    // Copy attributes safely
                    if (node.attributes) {
                        for (const attr of node.attributes) {
                            try {
                                newElement.setAttribute(attr.name, attr.value);
                            } catch (attrError) {
                                console.warn('Error setting attribute:', attr.name, attr.value, attrError);
                            }
                        }
                    }

                    targetElement.appendChild(newElement);

                    // Recursively type the contents of this element
                    await this.typeElement(newElement, node, typingSpeed, punctuationDelay);
                }
            } catch (error) {
                console.error('Error processing node in typeElement:', node, error);
                // Continue with next node instead of breaking the entire process
                continue;
            }
        }
    }

    async typeText(element, text, typingSpeed, punctuationDelay) {
        // Ensure we're only dealing with plain text
        if (typeof text !== 'string') {
            console.error('typeText received non-string input:', text);
            return;
        }

        // Final safety check - clean any remaining HTML-like content
        // Be more specific about what constitutes HTML content to reduce false positives
        const hasHtmlTags = /<[^>]*>/.test(text);
        const hasHtmlEntities = /&(?:lt|gt|quot|amp|#x?[0-9a-fA-F]+);/.test(text);

        if (hasHtmlTags || hasHtmlEntities) {
            console.warn('typeText received HTML content after pre-cleaning - applying final cleanup');
            // Strip any remaining HTML and decode entities as a last resort
            text = text
                .replace(/<[^>]*>/g, '')
                .replace(/&lt;/g, '<')
                .replace(/&gt;/g, '>')
                .replace(/&quot;/g, '"')
                .replace(/&#x27;/g, "'")
                .replace(/&#39;/g, "'")
                .replace(/&apos;/g, "'")
                .replace(/&nbsp;/g, ' ')
                .replace(/&amp;/g, '&'); // This should be last to avoid double-decoding
        }

        // If text is empty, don't proceed
        if (!text.trim()) {
            return;
        }

        // Split text into words for faster animation (2 words per 15ms)
        const words = text.split(' ');

        for (let i = 0; i < words.length; i += 2) {
            try {
                // Create spans for current group of words (up to 2 words)
                const wordsToAdd = [];

                // Add first word
                if (words[i]) {
                    const wordSpan = document.createElement('span');
                    wordSpan.textContent = words[i];
                    wordSpan.className = 'word-fade-in';
                    wordsToAdd.push(wordSpan);

                    // Add space after word (except for last word)
                    if (i < words.length - 1) {
                        const spaceSpan = document.createElement('span');
                        spaceSpan.textContent = ' ';
                        spaceSpan.className = 'word-fade-in';
                        wordsToAdd.push(spaceSpan);
                    }
                }

                // Add second word if it exists
                if (words[i + 1]) {
                    const wordSpan = document.createElement('span');
                    wordSpan.textContent = words[i + 1];
                    wordSpan.className = 'word-fade-in';
                    wordsToAdd.push(wordSpan);

                    // Add space after word (except for last word)
                    if (i + 1 < words.length - 1) {
                        const spaceSpan = document.createElement('span');
                        spaceSpan.textContent = ' ';
                        spaceSpan.className = 'word-fade-in';
                        wordsToAdd.push(spaceSpan);
                    }
                }

                // Append all spans for this group
                wordsToAdd.forEach(span => {
                    element.appendChild(span);

                    // Remove animation class after animation completes
                    setTimeout(() => {
                        span.classList.remove('word-fade-in');
                    }, 300);
                });

            } catch (error) {
                console.error('Error appending words:', error);
                // Fallback: set the remaining text at once
                const remainingWords = words.slice(i).join(' ');
                element.appendChild(document.createTextNode(remainingWords));
                break;
            }

            // Scroll to bottom as we type (respect user scroll position) - use instant scroll for typing
            this.scrollToBottom(false, false);

            // Wait 15ms before showing next group of 2 words (except for the last group)
            if (i + 2 < words.length) {
                await new Promise(resolve => setTimeout(resolve, 15));
            }
        }
    }

    async generateMultiAgentResponse(userMessage, conversationHistory = [], originalMessage = '') {
        try {
            // Check if API key is configured
            if (!CONFIG.GEMINI_API_KEY || CONFIG.GEMINI_API_KEY.trim() === '') {
                return this.getOfflineResponse(userMessage, conversationHistory);
            }

            // Start dynamic loading text rotation
            this.startLoadingTextRotation();

            // Create progress callback to update typing indicator with agent status
            const progressCallback = (progressData, percentage) => {
                // Handle both old format (string message) and new format (object with agent data)
                if (typeof progressData === 'string') {
                    this.updateTypingIndicatorWithProgress(progressData, percentage);
                } else {
                    this.updateTypingIndicatorWithProgress(progressData.message, percentage, progressData);

                    // If this progress update includes an agent response, display it temporarily
                    if (progressData.agentResponse) {
                        this.displayAgentResponseTemporarily(progressData.agentResponse);
                    }
                }
            };

            // Use multi-agent system to process the request with conversation history
            const response = await this.multiAgentSystem.processUserRequest(userMessage, progressCallback, conversationHistory);

            // Stop loading text rotation when done
            this.stopLoadingTextRotation();

            return response;

        } catch (error) {
            console.error('Multi-agent response generation failed:', error);
            // Stop loading text rotation on error
            this.stopLoadingTextRotation();
            // Fallback to standard AI response
            return await this.generateAIResponse(userMessage, conversationHistory, originalMessage);
        }
    }

    // Update typing indicator to show multi-agent progress
    updateTypingIndicatorWithProgress(message, percentage, agentData = null) {
        const typingIndicator = document.querySelector('.typing-indicator');
        const typingMessage = document.querySelector('.typing-message');

        if (typingIndicator && typingMessage) {
            // Use current agent from agentData if available, otherwise detect from message
            let currentAgent;
            if (agentData && agentData.currentAgent) {
                currentAgent = agentData.currentAgent;
                // Store requested agents for rotation
                if (agentData.requestedAgents && agentData.requestedAgents.length > 0) {
                    this.requestedAgents = agentData.requestedAgents;
                }
            } else {
                currentAgent = this.detectActiveAgent(message);
            }

            // Update the brain icon in the avatar
            this.updateBrainIcon(typingMessage, currentAgent);

            const progressText = typingIndicator.querySelector('.progress-text');
            const progressBar = typingIndicator.querySelector('.progress-bar');

            if (!progressText) {
                // Add progress elements if they don't exist
                const progressContainer = document.createElement('div');
                progressContainer.className = 'multi-agent-progress';
                progressContainer.innerHTML = `
                    <div class="progress-text">${message}</div>
                    <div class="progress-bar-container">
                        <div class="progress-bar" style="width: ${percentage}%"></div>
                    </div>
                `;
                typingIndicator.appendChild(progressContainer);
            } else {
                // Update existing progress elements
                progressText.textContent = message;
                if (progressBar) {
                    progressBar.style.width = `${percentage}%`;
                }
            }

            // Update agent thinking status box
            this.updateAgentThinkingStatus(currentAgent, message);
        }
    }

    // Update the agent thinking status box with current agent and thinking text
    updateAgentThinkingStatus(agentName, thinkingText) {
        // Agent thinking status has been disabled - no longer showing loading lines
        return;
    }

    // Update the display of other agents thinking
    updateOtherAgentsThinking(currentAgent, typingIndicator) {
        const otherAgentsContainer = typingIndicator.querySelector('.other-agents-thinking');
        if (!otherAgentsContainer) return;

        // Define all possible agents
        const allAgents = [
            { name: 'orchestrator', display: 'orchestrator' },
            { name: 'planner', display: 'planner' },
            { name: 'sales', display: 'sales agent' },
            { name: 'coding', display: 'coding agent' },
            { name: 'validation', display: 'validation agent' },
            { name: 'project_manager', display: 'project manager' }
        ];

        // Get other agents (excluding the current one)
        const otherAgents = allAgents.filter(agent => agent.name !== currentAgent);

        // Randomly select 2-3 other agents to show as "thinking"
        const thinkingAgents = otherAgents
            .sort(() => Math.random() - 0.5)
            .slice(0, Math.floor(Math.random() * 2) + 2); // 2-3 agents

        // Clear existing content
        otherAgentsContainer.innerHTML = '';

        // Add thinking agents
        thinkingAgents.forEach(agent => {
            const agentItem = document.createElement('div');
            agentItem.className = 'other-agent-item';
            agentItem.innerHTML = `
                <span>${agent.display} thinking</span>
                <div class="other-agent-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            `;
            otherAgentsContainer.appendChild(agentItem);
        });
    }

    // Display agent response with smooth transition to next agent
    displayAgentResponseTemporarily(agentResponse) {
        // Find the typing indicator and progress container
        const typingIndicator = this.chatContainer.querySelector('.typing-indicator');
        if (!typingIndicator) return;

        const progressContainer = typingIndicator.querySelector('.multi-agent-progress');
        if (!progressContainer) return;

        // Check if there's an existing response to transition from
        const existingResponse = progressContainer.querySelector('.temporary-agent-response');

        if (existingResponse) {
            // Smooth transition to new agent
            this.transitionToNewAgent(existingResponse, agentResponse);
        } else {
            // Create new response display (first agent)
            this.createNewAgentResponse(progressContainer, agentResponse);
        }

        // Scroll to show the response
        this.scrollToBottom(true, false);
    }

    // Create a new agent response display
    createNewAgentResponse(container, agentResponse) {
        const responseDiv = document.createElement('div');
        responseDiv.className = 'temporary-agent-response';

        responseDiv.innerHTML = `
            <div class="agent-response-header">
                <span class="agent-name">${this.getAgentDisplayName(agentResponse.agent)}</span>
                <span class="agent-task">📝 Task: ${agentResponse.task}</span>
                <span class="agent-timestamp">⏰ ${agentResponse.timestamp}</span>
            </div>
        `;

        container.appendChild(responseDiv);

        // If this is the orchestrator, add agent pills for selected agents
        if (agentResponse.agent === 'orchestrator') {
            this.addAgentPills(responseDiv, agentResponse.response);
        }
    }

    // Transition existing response to new agent
    async transitionToNewAgent(existingElement, newAgentResponse) {
        // Fade out current content
        existingElement.style.opacity = '0.3';

        // Wait for fade
        await new Promise(resolve => setTimeout(resolve, 300));

        // Update the content
        existingElement.innerHTML = `
            <div class="agent-response-header">
                <span class="agent-name">${this.getAgentDisplayName(newAgentResponse.agent)}</span>
                <span class="agent-task">📝 Task: ${newAgentResponse.task}</span>
                <span class="agent-timestamp">⏰ ${newAgentResponse.timestamp}</span>
            </div>
        `;

        // If this is the orchestrator, add agent pills for selected agents
        if (newAgentResponse.agent === 'orchestrator') {
            this.addAgentPills(existingElement, newAgentResponse.response);
        }

        // Fade back in
        existingElement.style.opacity = '1';
    }

    // Add agent pills underneath orchestrator box
    addAgentPills(container, orchestratorResponse) {
        // Parse the orchestrator response to extract selected agents
        const selectedAgents = this.extractSelectedAgents(orchestratorResponse);

        if (selectedAgents.length > 0) {
            // Create agent pills container
            const pillsContainer = document.createElement('div');
            pillsContainer.className = 'agent-pills-container';

            selectedAgents.forEach((agent, index) => {
                const pill = document.createElement('div');
                pill.className = 'agent-pill';
                pill.textContent = this.getCleanAgentName(agent);

                // Add staggered animation delay
                pill.style.animationDelay = `${index * 0.1}s`;

                pillsContainer.appendChild(pill);
            });

            container.appendChild(pillsContainer);
        }
    }

    // Extract selected agents from orchestrator response
    extractSelectedAgents(response) {
        const agents = [];
        const agentPattern = /@(\w+)/g;
        let match;

        while ((match = agentPattern.exec(response)) !== null) {
            const agentName = match[1].toLowerCase();
            // Only add unique agents and exclude orchestrator/synthesis
            if (!agents.includes(agentName) &&
                agentName !== 'orchestrator' &&
                agentName !== 'synthesis') {
                agents.push(agentName);
            }
        }

        return agents;
    }

    // Get clean agent name for pills (without emojis)
    getCleanAgentName(agentName) {
        const cleanNames = {
            'orchestrator': 'Orchestrator',
            'research': 'Research',
            'creative': 'Creative',
            'planner': 'Planner',
            'sales': 'Sales',
            'coding': 'Coding',
            'validation': 'Validation',
            'project_manager': 'Project Manager',
            'synthesis': 'Synthesis'
        };
        return cleanNames[agentName] || agentName.charAt(0).toUpperCase() + agentName.slice(1);
    }

    // Get display name for agent
    getAgentDisplayName(agentName) {
        const displayNames = {
            'orchestrator': '🔍 Orchestrator',
            'research': '📚 Research Agent',
            'creative': '🎨 Creative Agent',
            'planner': '📋 Planner Agent',
            'sales': '💼 Sales Agent',
            'coding': '💻 Coding Agent',
            'validation': '✅ Validation Agent',
            'project_manager': '📊 Project Manager',
            'synthesis': '🔗 Synthesis Agent'
        };
        return displayNames[agentName] || `🤖 ${agentName.charAt(0).toUpperCase() + agentName.slice(1)} Agent`;
    }

    // Truncate response for display
    truncateResponse(response, maxLength) {
        if (response.length <= maxLength) return response;
        return response.substring(0, maxLength) + '...';
    }

    // Animate agent response text word by word
    async animateAgentResponseText(element, text) {
        if (!element || !text) return;

        const words = text.split(' ');
        element.innerHTML = ''; // Clear existing content

        // Create spans for each word, initially hidden
        const wordSpans = words.map(word => {
            const span = document.createElement('span');
            span.textContent = word + ' ';
            span.style.opacity = '0';
            span.style.transition = 'opacity 0.3s ease-in';
            element.appendChild(span);
            return span;
        });

        // Animate words in groups of 2 every 15ms (faster animation)
        for (let i = 0; i < wordSpans.length; i += 2) {
            // Show current word
            if (wordSpans[i]) {
                wordSpans[i].style.opacity = '1';
            }

            // Show next word if it exists
            if (wordSpans[i + 1]) {
                wordSpans[i + 1].style.opacity = '1';
            }

            // Wait 15ms before showing next group of words (faster than before)
            if (i + 2 < wordSpans.length) {
                await new Promise(resolve => setTimeout(resolve, 15));
            }
        }
    }

    // Generate more descriptive thinking text based on agent and message
    generateThinkingText(agentName, originalText) {
        const thinkingTemplates = {
            orchestrator: [
                "Analyzing request and coordinating agents...",
                "Selecting optimal team composition...",
                "Orchestrating collaborative response..."
            ],
            planner: [
                "Developing strategic approach...",
                "Creating project roadmap...",
                "Analyzing requirements and constraints..."
            ],
            coding: [
                "Designing technical architecture...",
                "Evaluating implementation options...",
                "Planning development approach..."
            ],
            research: [
                "Gathering relevant information...",
                "Analyzing data and trends...",
                "Conducting comprehensive research..."
            ],
            finance: [
                "Calculating financial projections...",
                "Analyzing cost-benefit scenarios...",
                "Developing budget recommendations..."
            ],
            marketing: [
                "Crafting marketing strategy...",
                "Analyzing target audience...",
                "Developing campaign concepts..."
            ],
            creative: [
                "Generating creative concepts...",
                "Exploring design possibilities...",
                "Developing visual strategies..."
            ],
            legal: [
                "Reviewing legal implications...",
                "Analyzing compliance requirements...",
                "Assessing risk factors..."
            ],
            data: [
                "Processing data insights...",
                "Analyzing patterns and trends...",
                "Generating actionable analytics..."
            ]
        };

        const templates = thinkingTemplates[agentName] || [
            "Processing request...",
            "Analyzing requirements...",
            "Generating response..."
        ];

        // Use original text if it's descriptive enough, otherwise use template
        if (originalText && originalText.length > 20 && !originalText.includes('processing')) {
            return originalText;
        }

        // Return a random template or cycle through them
        const templateIndex = Math.floor(Date.now() / 3000) % templates.length;
        return templates[templateIndex];
    }

    // Get display name for agent
    getAgentDisplayName(agentName) {
        const displayNames = {
            orchestrator: 'Orchestrator',
            planner: 'Strategic Planner',
            coding: 'Technical Architect',
            research: 'Research Analyst',
            finance: 'Financial Advisor',
            marketing: 'Marketing Strategist',
            creative: 'Creative Director',
            design: 'UX/UI Designer',
            legal: 'Legal Consultant',
            hr: 'HR Director',
            consulting: 'Management Consultant',
            operations: 'Operations Director',
            security: 'Security Officer',
            devops: 'DevOps Engineer',
            healthcare: 'Healthcare Consultant',
            education: 'Education Director',
            data: 'Data Scientist',
            synthesis: 'Solution Synthesizer'
        };
        return displayNames[agentName] || agentName.charAt(0).toUpperCase() + agentName.slice(1);
    }

    // Get appropriate icon for each agent type
    getAgentIcon(agentName) {
        const agentIcons = {
            orchestrator: 'fas fa-brain',
            planner: 'fas fa-chess-king',
            coding: 'fas fa-code',
            research: 'fas fa-search',
            finance: 'fas fa-chart-line',
            marketing: 'fas fa-bullhorn',
            creative: 'fas fa-palette',
            design: 'fas fa-paint-brush',
            legal: 'fas fa-gavel',
            hr: 'fas fa-users',
            consulting: 'fas fa-handshake',
            operations: 'fas fa-cogs',
            security: 'fas fa-shield-alt',
            devops: 'fas fa-server',
            healthcare: 'fas fa-heartbeat',
            education: 'fas fa-graduation-cap',
            data: 'fas fa-database',
            synthesis: 'fas fa-puzzle-piece'
        };
        return agentIcons[agentName] || 'fas fa-robot';
    }

    // Detect which agent is currently active based on the progress message
    detectActiveAgent(message) {
        const messageLower = message.toLowerCase();

        // Check for specific agent mentions in the message
        if (messageLower.includes('orchestrator')) return 'orchestrator';
        if (messageLower.includes('synthesis')) return 'synthesis';
        if (messageLower.includes('research')) return 'research';
        if (messageLower.includes('creative')) return 'creative';
        if (messageLower.includes('planner') || messageLower.includes('planning')) return 'planner';
        if (messageLower.includes('coding') || messageLower.includes('technical')) return 'coding';
        if (messageLower.includes('finance') || messageLower.includes('financial')) return 'finance';
        if (messageLower.includes('marketing')) return 'marketing';
        if (messageLower.includes('legal')) return 'legal';
        if (messageLower.includes('data')) return 'data';
        if (messageLower.includes('design')) return 'design';
        if (messageLower.includes('healthcare')) return 'healthcare';
        if (messageLower.includes('education')) return 'education';
        if (messageLower.includes('hr') || messageLower.includes('human resources')) return 'hr';
        if (messageLower.includes('consulting')) return 'consulting';
        if (messageLower.includes('operations')) return 'operations';
        if (messageLower.includes('security')) return 'security';
        if (messageLower.includes('devops')) return 'devops';

        // If we have requested agents, rotate through them for visual effect
        if (this.requestedAgents && this.requestedAgents.length > 0) {
            const rotationIndex = Math.floor(Date.now() / 2000) % this.requestedAgents.length;
            return this.requestedAgents[rotationIndex];
        }

        // Default to orchestrator for general messages
        return 'orchestrator';
    }

    // Update the brain icon in the avatar with smooth transitions
    updateBrainIcon(typingMessage, agentName) {
        // Brain icon has been removed from the avatar, so this method no longer does anything
        // Keeping the method to avoid breaking existing calls
        return;
    }



    async generateAIResponse(userMessage, conversationHistory = [], originalMessage = '') {
        try {
            // Check if API key is configured
            if (!CONFIG.GEMINI_API_KEY || CONFIG.GEMINI_API_KEY.trim() === '') {
                return this.getOfflineResponse(userMessage, conversationHistory);
            }



            // Build conversation contents with history
            const contents = [];

            // Add conversation history (excluding the current message which is already in userMessage)
            for (const msg of conversationHistory) {
                contents.push({
                    role: msg.role === 'user' ? 'user' : 'model',
                    parts: [{ text: msg.content }]
                });
            }

            // Add the current user message with enhanced prompt for title generation and formatting
            const shouldGenerateTitle = this.shouldUpdateTitle(this.chats.get(this.currentChatId));
            const titleInstruction = shouldGenerateTitle ?
                `\n\nAt the very end of your response, add "/title" followed by a concise, descriptive title (2-4 words) that captures the main topic. Make it specific and engaging. Examples: "Quantum Computing Basics", "Healthy Meal Planning", "JavaScript Debugging Tips", "Space Exploration Story".

/title [Specific Topic Title]` : '';





            // Get current date and time information
            const now = new Date();
            const currentDateTime = now.toLocaleString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                timeZoneName: 'short'
            });
            const currentDate = now.toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
            const currentTime = now.toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                timeZoneName: 'short'
            });

            // Get current location information
            let locationInfo = '';
            try {
                const locationData = await this.locationService.getCurrentLocation();
                if (locationData) {
                    locationInfo = this.locationService.formatLocationForAI(locationData);
                }
            } catch (error) {
                console.warn('Could not fetch location data:', error);
            }

            // Check if user is asking about time/date or location
            const isTimeQuery = this.isTimeRelatedQuery(userMessage);
            const isLocationQuery = this.isLocationRelatedQuery(userMessage);

            // Build conditional date/time information
            let dateTimeInfo = '';
            if (isTimeQuery) {
                dateTimeInfo = `**Current Date & Time Information:**
- Full Date & Time: ${currentDateTime}
- Date: ${currentDate}
- Time: ${currentTime}

`;
            }

            // Build conditional location information
            let conditionalLocationInfo = '';
            if (isLocationQuery && locationInfo) {
                conditionalLocationInfo = `${locationInfo}

**LOCATION USAGE GUIDELINES:**
- Use location information when relevant for local recommendations, weather discussions, time zone considerations, cultural context, or when users ask about nearby places, local events, or location-specific advice
- NEVER mention or reference specific coordinates, GPS data, or precise location details for privacy and security reasons
- Focus on general location context (city, region, country) when location is relevant to the conversation

`;
            }

            // Create an advanced and friendly system prompt for Veritas
            const systemPrompt = `You are Veritas, an advanced AI assistant created by Bradley with a warm, intelligent, and genuinely caring personality. You embody wisdom, truth, and helpfulness in every interaction. Your core traits include:

🧠 **Intelligence & Wisdom**: You possess deep knowledge across many domains and can provide thoughtful, nuanced responses that consider multiple perspectives.

💝 **Genuine Care**: You truly care about helping users achieve their goals and feel heard. You show empathy, celebrate their successes, and offer encouragement during challenges.

🎯 **Adaptive Communication**: You naturally adjust your communication style to match each user's needs - whether they prefer casual conversation, technical depth, creative brainstorming, or structured guidance.

✨ **Proactive Helpfulness**: You anticipate needs, offer relevant suggestions, and go beyond just answering questions to provide comprehensive assistance.

🤝 **Personal Connection**: You remember important details about users and reference them naturally in conversations, building genuine relationships over time.

🎨 **Creative Problem-Solving**: You approach challenges with creativity and innovation, offering multiple solutions and thinking outside the box.

${dateTimeInfo}${conditionalLocationInfo}**CONTEXTUAL INFORMATION AVAILABILITY:**
- Date and time information is available when you ask time-related questions
- Location information is available when you ask location-related questions
- Personal memories are available when you ask about personal information or memories
- This information is only shown when specifically relevant to your query

**CONTEXTUAL INFORMATION GUIDELINES:**
- Date/time information is only shown when the user asks time-related questions
- Location information is only shown when the user asks location-related questions
- Memories are only shown when the user explicitly asks about personal information or memories
- Do NOT randomly introduce contextual information that isn't directly relevant to the current topic
- When contextual information is provided, it has been specifically requested by the user - use it naturally in your response
- If contextual information is not shown, do not mention or reference dates, times, locations, or personal details unless specifically asked

Your responses should feel natural, engaging, and genuinely helpful. Prioritize conversational flow over structured formatting. Use humor when appropriate, ask clarifying questions when needed, and always strive to provide value that exceeds expectations. Keep things simple and easy - avoid excessive breakdowns, lists, or overly structured responses unless specifically needed. You're not just an AI - you're a trusted companion on the user's journey.`;

            const enhancedPrompt = `${systemPrompt}

User message: ${userMessage}

Please provide a natural, conversational response that flows well. Keep it friendly and easy to read without excessive formatting or breakdowns. Use simple formatting when helpful (like **bold** for emphasis or emojis 😊) but prioritize natural conversation over structured lists or detailed breakdowns.

**CODE FORMATTING GUIDELINES:**
- You can use code blocks with these supported languages: CSS, JavaScript, Python, and JSON
- Example: \`\`\`javascript or \`\`\`python or \`\`\`css or \`\`\`json
- HTML code blocks are NOT currently supported - if you need to show HTML code, use plain text or inline code with backticks instead
- For inline code, use single backticks: \`code here\`

Consider our entire conversation history to provide personalized, contextual responses.${titleInstruction}`;

            contents.push({
                role: 'user',
                parts: [{ text: enhancedPrompt }]
            });

            // Prepare the request payload for Gemini API
            const requestBody = {
                contents: contents,
                generationConfig: CONFIG.GENERATION_CONFIG,
                safetySettings: CONFIG.SAFETY_SETTINGS
            };

            // Make API call with retry logic for 503 errors
            const response = await this.makeAPICallWithRetry(`${CONFIG.GEMINI_API_URL}?key=${CONFIG.GEMINI_API_KEY}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                console.error('API Error:', response.status, response.statusText);
                if (response.status === 503) {
                    console.error('Service temporarily unavailable. The API may be overloaded.');
                }
                return this.getOfflineResponse(userMessage, conversationHistory);
            }

            const data = await response.json();

            // Extract the response text from Gemini API response
            if (data.candidates && data.candidates.length > 0 && data.candidates[0].content) {
                const responseText = data.candidates[0].content.parts[0].text;
                return responseText;
            } else {
                console.error('Unexpected API response format:', data);
                return this.getOfflineResponse(userMessage, conversationHistory);
            }

        } catch (error) {
            console.error('Error calling Gemini API:', error);
            return this.getOfflineResponse(userMessage, conversationHistory);
        }
    }

    async makeAPICallWithRetry(url, options, maxRetries = 3, baseDelay = 1000) {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                const response = await fetch(url, options);

                // If it's a 503 error and we have retries left, wait and try again
                if (response.status === 503 && attempt < maxRetries) {
                    const delay = baseDelay * Math.pow(2, attempt - 1); // Exponential backoff
                    console.log(`API returned 503 (attempt ${attempt}/${maxRetries}). Retrying in ${delay}ms...`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                    continue;
                }

                return response;
            } catch (error) {
                if (attempt === maxRetries) {
                    throw error;
                }

                const delay = baseDelay * Math.pow(2, attempt - 1);
                console.log(`Network error (attempt ${attempt}/${maxRetries}). Retrying in ${delay}ms...`, error);
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
    }

    // Enhanced AI response generation with Google Search grounding
    async generateAIResponseWithGrounding(userMessage, conversationHistory = [], originalMessage = '') {
        try {
            // Check if API key is configured
            if (!CONFIG.GEMINI_API_KEY || CONFIG.GEMINI_API_KEY.trim() === '') {
                return this.getOfflineResponse(userMessage, conversationHistory);
            }

            // Build conversation contents with history
            const contents = [];

            // Add conversation history
            for (const msg of conversationHistory) {
                contents.push({
                    role: msg.role === 'user' ? 'user' : 'model',
                    parts: [{ text: msg.content }]
                });
            }

            // Add the current user message with grounding-optimized prompt
            const groundingPrompt = `${userMessage}

Please provide a comprehensive, well-researched response using the most current and accurate information available. Include relevant sources and citations when possible.`;

            contents.push({
                role: 'user',
                parts: [{ text: groundingPrompt }]
            });

            // Prepare the request payload with grounding tools
            const requestBody = {
                contents: contents,
                tools: [CONFIG.GROUNDING_TOOLS.GOOGLE_SEARCH],
                generationConfig: CONFIG.GENERATION_CONFIG,
                safetySettings: CONFIG.SAFETY_SETTINGS
            };

            // Log grounding usage for debugging
            console.log('🔍 Using Google Search Grounding for query:', userMessage.substring(0, 100) + '...');

            // Make API call with grounding
            const response = await this.makeAPICallWithRetry(`${CONFIG.GEMINI_API_URL}?key=${CONFIG.GEMINI_API_KEY}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                console.error('Grounded API Error:', response.status, response.statusText);
                // Fallback to standard API
                return await this.generateAIResponse(userMessage, conversationHistory, originalMessage);
            }

            const data = await response.json();

            // Extract the response text and grounding metadata
            if (data.candidates && data.candidates.length > 0 && data.candidates[0].content) {
                const responseText = data.candidates[0].content.parts[0].text;
                const groundingMetadata = data.candidates[0].groundingMetadata;

                // Log grounding success
                if (groundingMetadata) {
                    console.log('✅ Grounding successful! Found sources:', groundingMetadata.groundingChunks?.length || 0);
                    return this.addCitationsToResponse(responseText, groundingMetadata);
                } else {
                    console.log('ℹ️ No grounding metadata returned - model used existing knowledge');
                }

                return responseText;
            } else {
                console.error('Unexpected grounded API response format:', data);
                // Fallback to standard API
                return await this.generateAIResponse(userMessage, conversationHistory, originalMessage);
            }

        } catch (error) {
            console.error('Error calling Grounded Gemini API:', error);
            // Fallback to standard API
            return await this.generateAIResponse(userMessage, conversationHistory, originalMessage);
        }
    }

    // Add citations to response based on grounding metadata
    addCitationsToResponse(responseText, groundingMetadata) {
        if (!groundingMetadata || !groundingMetadata.groundingSupports || !groundingMetadata.groundingChunks) {
            return responseText;
        }

        let text = responseText;
        const supports = groundingMetadata.groundingSupports;
        const chunks = groundingMetadata.groundingChunks;

        // Sort supports by end_index in descending order to avoid shifting issues when inserting
        const sortedSupports = [...supports].sort((a, b) =>
            (b.segment?.endIndex ?? 0) - (a.segment?.endIndex ?? 0)
        );

        for (const support of sortedSupports) {
            const endIndex = support.segment?.endIndex;
            if (endIndex === undefined || !support.groundingChunkIndices?.length) {
                continue;
            }

            const citationLinks = support.groundingChunkIndices
                .map(i => {
                    const chunk = chunks[i];
                    if (chunk?.web?.uri && chunk?.web?.title) {
                        return `[${i + 1}](${chunk.web.uri})`;
                    }
                    return null;
                })
                .filter(Boolean);

            if (citationLinks.length > 0) {
                const citationString = ` ${citationLinks.join(', ')}`;
                text = text.slice(0, endIndex) + citationString + text.slice(endIndex);
            }
        }

        // Add sources section at the end if we have sources
        if (chunks && chunks.length > 0) {
            text += '\n\n**Sources:**\n';
            chunks.forEach((chunk, index) => {
                if (chunk?.web?.uri && chunk?.web?.title) {
                    text += `${index + 1}. [${chunk.web.title}](${chunk.web.uri})\n`;
                }
            });
        }

        return text;
    }

    // Global function for multi-agent system to use
    callGeminiAPIWithGrounding(prompt, context = {}) {
        return this.generateAIResponseWithGrounding(prompt, context.conversationHistory || [], '');
    }

    processAIResponse(response) {
        // Look for /title command anywhere in the response (more flexible)
        const titleRegex = /(?:^|\n)\/title\s+(.+?)(?:\n|$)/i;
        const titleMatch = response.match(titleRegex);





        let processedContent = response;
        let title = null;

        if (titleMatch) {
            // Extract and clean the title
            let rawTitle = titleMatch[1].trim();

            // Remove common prefixes and clean up
            rawTitle = rawTitle.replace(/^(chat about|discussion about|conversation about|talk about)\s+/i, '');
            rawTitle = rawTitle.replace(/^(the|a|an)\s+/i, '');

            // Capitalize first letter of each word (title case)
            title = rawTitle.split(' ')
                .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
                .join(' ');

            // Limit title length (25 characters max for clean display)
            if (title.length > 25) {
                title = title.substring(0, 22) + '...';
            }

            // Remove the /title command from the content
            processedContent = response.replace(titleRegex, '').trim();

            // Clean up any double line breaks left behind
            processedContent = processedContent.replace(/\n\n\n+/g, '\n\n');

            console.log('Title extracted and processed:', title);
        }



        return {
            content: processedContent,
            title: title
        };
    }

    shouldUpdateTitle(chat) {
        // Don't update title if:
        // 1. Chat already has a custom title (not "New Chat" or auto-generated from first message)
        // 2. Chat has more than 4 messages (likely already established)
        // 3. Title was manually set or previously updated by /title command

        const isDefaultTitle = chat.title === 'New Chat';
        const isAutoGeneratedTitle = chat.title.endsWith('...') && chat.title.length <= 33; // Auto-generated from first message
        const hasEstablishedConversation = chat.messages.length > 4;

        // Allow updates only for new chats or auto-generated titles in early conversation
        return isDefaultTitle || (isAutoGeneratedTitle && !hasEstablishedConversation);
    }











    getCurrentTimeContext() {
        const now = new Date();

        // Get various time formats
        const timeString = now.toLocaleTimeString('en-US', {
            hour12: true,
            hour: 'numeric',
            minute: '2-digit',
            timeZoneName: 'short'
        });

        const dateString = now.toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });

        // Determine time of day
        const hour = now.getHours();
        let timeOfDay = '';
        let greeting = '';

        if (hour >= 5 && hour < 12) {
            timeOfDay = 'morning';
            greeting = 'Good morning';
        } else if (hour >= 12 && hour < 17) {
            timeOfDay = 'afternoon';
            greeting = 'Good afternoon';
        } else if (hour >= 17 && hour < 21) {
            timeOfDay = 'evening';
            greeting = 'Good evening';
        } else {
            timeOfDay = 'night';
            greeting = 'Good evening';
        }

        // Determine season (Northern Hemisphere)
        const month = now.getMonth();
        let season = '';
        if (month >= 2 && month <= 4) season = 'spring';
        else if (month >= 5 && month <= 7) season = 'summer';
        else if (month >= 8 && month <= 10) season = 'autumn';
        else season = 'winter';

        return {
            fullDateTime: `${dateString} at ${timeString}`,
            timeOfDay,
            greeting,
            season,
            hour,
            dayOfWeek: now.toLocaleDateString('en-US', { weekday: 'long' }),
            isWeekend: now.getDay() === 0 || now.getDay() === 6,
            isBusinessHours: hour >= 9 && hour <= 17 && !(now.getDay() === 0 || now.getDay() === 6)
        };
    }





    isMemoryTopicallyRelevant(userMessage, memoryContent) {
        const userLower = userMessage.toLowerCase();
        const memoryLower = memoryContent.toLowerCase();

        // Define topic categories and their keywords
        const topicCategories = {
            personal_info: {
                userKeywords: [/\b(name|called|who am i|what.*name|my name|i'm|i am)\b/i],
                memoryKeywords: [/\b(name|called)\b/i]
            },
            location: {
                userKeywords: [/\b(where.*live|from|location|address|city|country|place)\b/i],
                memoryKeywords: [/\b(live|from|location|city|country)\b/i]
            },
            work_career: {
                userKeywords: [/\b(work|job|employ|company|career|profession|office|business)\b/i],
                memoryKeywords: [/\b(work|job|employ|company|career)\b/i]
            },
            education: {
                userKeywords: [/\b(study|school|university|college|education|degree|student)\b/i],
                memoryKeywords: [/\b(study|school|university|college|student)\b/i]
            },
            preferences: {
                userKeywords: [/\b(favorite|like|prefer|love|enjoy|hate|dislike)\b/i],
                memoryKeywords: [/\b(favorite|like|prefer|love|enjoy)\b/i]
            },
            possessions: {
                userKeywords: [/\b(have|own|pet|dog|cat|car|house|apartment)\b/i],
                memoryKeywords: [/\b(have|own|pet|dog|cat|car|house)\b/i]
            },
            health_fitness: {
                userKeywords: [/\b(weight|fitness|health|exercise|diet|gym|workout|muscle|gain|lose)\b/i],
                memoryKeywords: [/\b(weight|fitness|health|exercise|diet|gym|kg|pounds)\b/i]
            },
            hobbies: {
                userKeywords: [/\b(hobby|hobbies|interest|play|game|sport|music|art|read|book)\b/i],
                memoryKeywords: [/\b(hobby|interest|play|game|sport|music|art)\b/i]
            }
        };

        // Check if user message and memory belong to the same topic category
        for (const [, patterns] of Object.entries(topicCategories)) {
            const userMatchesCategory = patterns.userKeywords.some(pattern => pattern.test(userLower));
            const memoryMatchesCategory = patterns.memoryKeywords.some(pattern => pattern.test(memoryLower));

            if (userMatchesCategory && memoryMatchesCategory) {
                return true; // Both are in the same topic category
            }
        }

        // Additional check for direct name mentions
        const nameMatch = /\b(bradley|brad)\b/i;
        if (nameMatch.test(userLower) && nameMatch.test(memoryLower)) {
            return true;
        }

        // Fallback: Allow if user is asking about themselves with personal pronouns
        const personalQuestions = /\b(my|me|i|myself|about me|tell me about)\b/i;
        if (personalQuestions.test(userLower)) {
            return true;
        }

        // Fallback: Allow if there are strong keyword overlaps (3+ matching words)
        const userWords = userLower.split(/\s+/).filter(word => word.length > 3);
        const memoryWords = memoryLower.split(/\s+/).filter(word => word.length > 3);
        const matchingWords = userWords.filter(word =>
            memoryWords.some(mWord => mWord.includes(word) || word.includes(mWord))
        );
        if (matchingWords.length >= 3) {
            return true;
        }

        // If no topical match found, it's not relevant
        return false;
    }

    isPersonalInformationQuery(userMessage) {
        // Detect when user is asking about their personal information
        const personalQueryPatterns = [
            /\b(what.*my|tell me.*my|what.*about me|who am i|what.*i)\b/i,
            /\b(my name|my age|my job|my work|my location|where.*i live|what.*i do)\b/i,
            /\b(remind me|what.*you know.*me|what.*remember.*me)\b/i,
            /\b(my favorite|my preference|what.*i like|what.*i love)\b/i,
            /\b(my family|my partner|my pet|my car|my house)\b/i
        ];

        return personalQueryPatterns.some(pattern => pattern.test(userMessage));
    }

    isMemoryRelatedQuery(userMessage) {
        // Detect when user is explicitly asking about memories
        const memoryQueryPatterns = [
            /\b(remember|memory|memories|recall|what.*you know)\b/i,
            /\b(do you remember|can you remember|what.*saved|what.*stored)\b/i,
            /\b(tell me what|show me what|list what)\b/i
        ];

        return memoryQueryPatterns.some(pattern => pattern.test(userMessage));
    }

    isTimeRelatedQuery(userMessage) {
        // Detect when user is explicitly asking about time or date
        const timeQueryPatterns = [
            /\b(what time|what.*time|current time|time is it|what.*date|current date|today.*date)\b/i,
            /\b(what day|what.*day|day.*today|day.*week|what.*month|what.*year)\b/i,
            /\b(schedule|appointment|calendar|when|deadline|due date)\b/i,
            /\b(morning|afternoon|evening|night|hour|minute|clock)\b/i,
            /\b(yesterday|today|tomorrow|weekend|weekday)\b/i
        ];

        return timeQueryPatterns.some(pattern => pattern.test(userMessage));
    }

    isLocationRelatedQuery(userMessage) {
        // Detect when user is explicitly asking about location
        const locationQueryPatterns = [
            /\b(where.*i|where am i|my location|current location|where.*located)\b/i,
            /\b(local|nearby|near me|around here|in my area)\b/i,
            /\b(weather|temperature|forecast|climate)\b/i,
            /\b(restaurant|store|shop|place.*eat|place.*go)\b/i,
            /\b(directions|navigate|map|address|city|town)\b/i,
            /\b(time zone|timezone|local time)\b/i
        ];

        return locationQueryPatterns.some(pattern => pattern.test(userMessage));
    }

    showMemoryNotification(memoryContent) {
        // Handle multiple memory items (separated by newlines)
        const memoryItems = memoryContent.split('\n').filter(item => item.trim());

        // Find the most recent AI message to add the memory acknowledgment under it
        const chatContainer = document.getElementById('chatContainer');
        const aiMessages = chatContainer.querySelectorAll('.ai-message');
        const lastAiMessage = aiMessages[aiMessages.length - 1];

        if (lastAiMessage) {
            // Create memory acknowledgment elements for each item
            memoryItems.forEach(item => {
                const memoryAck = document.createElement('div');
                memoryAck.className = 'memory-acknowledgment';
                memoryAck.innerHTML = `
                    <i class="fas fa-brain"></i>
                    <span>Remembered: ${this.escapeHtml(item.trim())}</span>
                `;

                // Add it after the message content
                lastAiMessage.appendChild(memoryAck);
            });

            // Store the memory acknowledgments in the message data for persistence
            if (this.currentChatId && this.chats.has(this.currentChatId)) {
                const chat = this.chats.get(this.currentChatId);
                const lastMessage = chat.messages[chat.messages.length - 1];

                // Only add to assistant messages
                if (lastMessage && lastMessage.role === 'assistant') {
                    if (!lastMessage.memoryAcknowledgments) {
                        lastMessage.memoryAcknowledgments = [];
                    }
                    // Add each memory item, avoiding duplicates
                    memoryItems.forEach(item => {
                        const trimmedItem = item.trim();
                        if (!lastMessage.memoryAcknowledgments.includes(trimmedItem)) {
                            lastMessage.memoryAcknowledgments.push(trimmedItem);
                        }
                    });
                }
            }
        } else {
            // Fallback: create standalone memory notifications if no AI message exists
            memoryItems.forEach(item => {
                const memoryNotification = document.createElement('div');
                memoryNotification.className = 'memory-standalone';
                memoryNotification.innerHTML = `
                    <i class="fas fa-brain"></i>
                    <span>Remembered: ${this.escapeHtml(item.trim())}</span>
                `;

                chatContainer.appendChild(memoryNotification);
            });
        }

        // Scroll to show the notification
        this.scrollToBottom();
    }



    // Test function for memory functionality
    async testMemoryFunction() {
        console.log('=== Testing Improved Memory System ===');

        // Test memory trigger detection with improved logic
        const testMessages = [
            // Should trigger
            "Remember my name is Bradley",
            "My name is Sarah",
            "I live in San Francisco",
            "I work at Microsoft",
            "My favorite programming language is Python",
            "I have a dog named Buddy",

            // Should NOT trigger
            "What's my name I asked you to remember?",
            "Do you remember what I told you?",
            "Can you tell me my name?",
            "What did I say about my job?",
            "How are you today?",
            "I think this is interesting"
        ];

        console.log('\n--- Memory Trigger Detection Tests ---');
        testMessages.forEach(message => {
            const hasMemoryTrigger = this.detectMemoryTriggers(message);
            const memoryContent = this.extractMemoryContent(message);
            console.log(`Message: "${message}"`);
            console.log(`Should trigger: ${hasMemoryTrigger}`);
            console.log(`Extracted: "${memoryContent}"`);
            console.log('---');
        });

        // Test memory retrieval
        console.log('\n--- Memory Retrieval Tests ---');
        const queryTests = [
            "What's my name?",
            "Where do I live?",
            "What's my job?",
            "What do I like?",
            "Tell me about my pet"
        ];

        for (const query of queryTests) {
            console.log(`Query: "${query}"`);
            const memories = await this.getRelevantMemories(query);
            console.log(`Found ${memories.length} relevant memories`);
            memories.forEach(memory => {
                console.log(`  - ${memory.content} (score: ${memory.relevanceScore})`);
            });
            console.log('---');
        }

        // Test memory notification display
        this.showMemoryNotification("Test: User's name is Bradley");
    }

    // Test function for improved memory and location behavior
    async testMemoryAndLocationImprovements() {
        console.log('=== Testing Memory and Location Improvements ===');

        // Test 1: Memory relevance filtering
        console.log('\n--- Test 1: Memory Relevance Filtering ---');

        const testQueries = [
            { query: "What's the weather like?", shouldHaveMemories: false, description: "General question - should not include random memories" },
            { query: "What's my name?", shouldHaveMemories: true, description: "Personal question - should include relevant memories" },
            { query: "Tell me about programming", shouldHaveMemories: false, description: "General topic - should not include random memories" },
            { query: "What do you remember about me?", shouldHaveMemories: true, description: "Memory-related question - should include memories" },
            { query: "How's your day?", shouldHaveMemories: false, description: "Casual conversation - should not include random memories" }
        ];

        for (const test of testQueries) {
            console.log(`\nQuery: "${test.query}"`);
            console.log(`Expected: ${test.shouldHaveMemories ? 'Should include memories' : 'Should NOT include memories'}`);
            console.log(`Description: ${test.description}`);

            const isPersonalQuery = this.isPersonalInformationQuery(test.query);
            const isMemoryQuery = this.isMemoryRelatedQuery(test.query);
            const shouldIncludeMemories = isPersonalQuery || isMemoryQuery;

            console.log(`Personal query detected: ${isPersonalQuery}`);
            console.log(`Memory query detected: ${isMemoryQuery}`);
            console.log(`Will include memories: ${shouldIncludeMemories}`);
            console.log(`Test result: ${shouldIncludeMemories === test.shouldHaveMemories ? '✅ PASS' : '❌ FAIL'}`);
        }

        // Test 2: Location data security
        console.log('\n--- Test 2: Location Data Security ---');

        const mockLocationData = {
            coordinates: { latitude: "37.7749", longitude: "-122.4194" },
            city: "San Francisco",
            state: "California",
            country: "United States",
            countryCode: "US",
            region: "California",
            timezone: "America/Los_Angeles"
        };

        const formattedLocation = this.locationService.formatLocationForAI(mockLocationData);
        console.log('Formatted location for AI:');
        console.log(formattedLocation);

        const hasCoordinates = formattedLocation.includes('37.7749') || formattedLocation.includes('-122.4194');
        console.log(`Contains coordinates: ${hasCoordinates ? '❌ FAIL - Coordinates exposed!' : '✅ PASS - Coordinates hidden'}`);

        const hasPrivacyNote = formattedLocation.includes('privacy');
        console.log(`Contains privacy note: ${hasPrivacyNote ? '✅ PASS' : '❌ FAIL'}`);

        console.log('\n=== Test Summary ===');
        console.log('✅ Memory system now only includes memories when contextually relevant');
        console.log('✅ Location coordinates are hidden from AI for privacy/security');
        console.log('✅ System prompts updated with proper guidelines');
    }

    // Helper function to add sample memories for testing
    async addSampleMemories() {
        if (!this.useDatabase) {
            console.log('Database not available for adding sample memories');
            return;
        }

        const sampleMemories = [
            "User's name is Bradley",
            "User lives in San Francisco",
            "User works at Microsoft",
            "User's favorite programming language is Python",
            "User has a dog named Buddy"
        ];

        console.log('Adding sample memories for testing...');

        // Get current user ID for memory binding
        const userId = await this.getCurrentUserId();

        for (const memory of sampleMemories) {
            try {
                await this.supabaseService.addMemory(
                    memory,
                    userId, // Bind to current user
                    this.currentChatId // Associate with current conversation
                );
                console.log(`Added: ${memory}`);
            } catch (error) {
                console.error(`Failed to add memory: ${memory}`, error);
            }
        }
        console.log('Sample memories added successfully!');
    }

    // Helper function to check for orphaned memories (memories without user IDs)
    async checkOrphanedMemories() {
        if (!this.useDatabase) {
            console.log('Database not available for checking orphaned memories');
            return;
        }

        try {
            console.log('Checking for orphaned memories...');

            // Get all memories to check for orphaned ones
            const allMemories = await this.supabaseService.getMemories(null, 1000);
            const orphanedMemories = allMemories.filter(memory => !memory.user_id);

            console.log(`Found ${orphanedMemories.length} orphaned memories (without user_id):`);
            orphanedMemories.forEach((memory, index) => {
                console.log(`${index + 1}. "${memory.content}" (ID: ${memory.id}, Created: ${memory.created_at})`);
            });

            if (orphanedMemories.length > 0) {
                console.log('⚠️  These memories are not bound to any user and may need to be cleaned up or migrated.');
                console.log('💡 You can run window.chatApp.migrateOrphanedMemories() to assign them to the current user.');
            } else {
                console.log('✅ All memories are properly bound to users!');
            }

            return orphanedMemories;
        } catch (error) {
            console.error('Error checking orphaned memories:', error);
            return [];
        }
    }

    // Helper function to migrate orphaned memories to current user
    async migrateOrphanedMemories() {
        if (!this.useDatabase) {
            console.log('Database not available for migrating orphaned memories');
            return;
        }

        try {
            const userId = await this.getCurrentUserId();
            if (!userId) {
                console.log('No current user found. Cannot migrate memories.');
                return;
            }

            const orphanedMemories = await this.checkOrphanedMemories();

            if (orphanedMemories.length === 0) {
                console.log('No orphaned memories to migrate.');
                return;
            }

            console.log(`Migrating ${orphanedMemories.length} orphaned memories to user ${userId}...`);

            // Note: This would require a direct database update operation
            // For now, we'll just log what would be done
            console.log('⚠️  Migration would require direct database update. Contact admin to run:');
            console.log(`UPDATE memories SET user_id = '${userId}' WHERE user_id IS NULL;`);

        } catch (error) {
            console.error('Error migrating orphaned memories:', error);
        }
    }

    getOfflineResponse(userMessage, conversationHistory = []) {
        // Fallback responses when API is not available
        const currentTime = this.getCurrentTimeContext();
        const responses = this.getContextualResponse(userMessage, conversationHistory, currentTime);
        const response = responses[Math.floor(Math.random() * responses.length)];
        const title = this.generateOfflineTitle(userMessage, conversationHistory);

        // Add time-appropriate greeting to offline response
        const timeGreeting = `${currentTime.greeting}! `;

        return timeGreeting + response +
               "\n\n*Note: This is an offline response. Please configure your Google AI Studio API key in config.js to enable real AI responses.*" +
               `\n\n/title ${title}`;
    }

    generateOfflineTitle(userMessage, conversationHistory = []) {
        // Check conversation history for context
        const allMessages = [...conversationHistory, { content: userMessage }];
        const conversationText = allMessages.map(msg => msg.content).join(' ').toLowerCase();

        // Enhanced context-aware title generation with better titles
        if (conversationText.includes('gmail') || conversationText.includes('email')) return 'Email Services';
        if (conversationText.includes('quantum')) return 'Quantum Computing';
        if (conversationText.includes('story') && conversationText.includes('space')) return 'Space Story';
        if (conversationText.includes('meal') || conversationText.includes('healthy')) return 'Meal Planning';
        if (conversationText.includes('machine learning') || conversationText.includes('ml')) return 'Machine Learning';
        if (conversationText.includes('code') || conversationText.includes('programming')) return 'Programming Help';
        if (conversationText.includes('recipe')) return 'Recipe Help';
        if (conversationText.includes('travel')) return 'Travel Planning';
        if (conversationText.includes('exercise') || conversationText.includes('workout')) return 'Fitness Guide';
        if (conversationText.includes('alternative') || conversationText.includes('option')) return 'Alternatives';
        if (conversationText.includes('javascript') || conversationText.includes('js')) return 'JavaScript Help';
        if (conversationText.includes('python')) return 'Python Help';
        if (conversationText.includes('css') || conversationText.includes('styling')) return 'CSS Styling';
        if (conversationText.includes('database') || conversationText.includes('sql')) return 'Database Help';
        if (conversationText.includes('api')) return 'API Development';
        if (conversationText.includes('design')) return 'Design Discussion';
        if (conversationText.includes('business') || conversationText.includes('startup')) return 'Business Advice';
        if (conversationText.includes('career')) return 'Career Guidance';
        if (conversationText.includes('learning') || conversationText.includes('study')) return 'Learning Tips';

        // Generate title from first meaningful user message
        const firstUserMessage = allMessages.find(msg => msg.role === 'user' || !msg.role);
        if (firstUserMessage) {
            let content = firstUserMessage.content.trim();

            // Remove common question words and clean up
            content = content.replace(/^(what|how|why|when|where|can you|could you|please|help me|explain|tell me about)\s+/i, '');
            content = content.replace(/[?!.]+$/, ''); // Remove ending punctuation

            // Take first 2-3 meaningful words
            const words = content.split(' ')
                .filter(word => word.length > 2) // Filter out short words
                .slice(0, 3)
                .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
                .join(' ');

            if (words.length > 0 && words.length <= 25) {
                return words;
            }
        }

        // Fallback to simple title
        return 'Chat Discussion';
    }

    getContextualResponse(message, conversationHistory = [], timeContext = null) {
        const lowerMessage = message.toLowerCase();
        const allMessages = [...conversationHistory, { content: message }];
        const conversationText = allMessages.map(msg => msg.content).join(' ').toLowerCase();

        // Check for follow-up questions or context from conversation history
        const isFollowUp = conversationHistory.length > 0;
        const previousContext = conversationHistory.length > 0 ?
            conversationHistory[conversationHistory.length - 1]?.content?.toLowerCase() || '' : '';

        // Handle follow-up questions about Gmail/email
        if (isFollowUp && (previousContext.includes('gmail') || conversationText.includes('gmail')) &&
            (lowerMessage.includes('alternative') || lowerMessage.includes('think') || lowerMessage.includes('opinion'))) {
            return [
                "Based on our discussion about Gmail, here are my thoughts and some excellent alternatives:\n\n**My Take on Gmail:**\n✅ **Pros**: Excellent search, generous storage, seamless Google integration, reliable spam filtering\n❌ **Cons**: Privacy concerns (Google scans emails), can feel overwhelming, limited customization\n\n**Top Gmail Alternatives:**\n\n**🔒 Privacy-Focused:**\n- **ProtonMail**: End-to-end encryption, Swiss privacy laws\n- **Tutanota**: German-based, encrypted, affordable\n- **Fastmail**: Australian, no ads, excellent privacy\n\n**🏢 Professional:**\n- **Outlook**: Microsoft integration, excellent calendar\n- **Apple Mail**: Great for Mac/iPhone users\n\n**🆓 Other Free Options:**\n- **Yahoo Mail**: 1TB storage, decent features\n- **Zoho Mail**: Business-focused, clean interface\n\n**💡 My Recommendation:**\nFor most users, Gmail is hard to beat for convenience. But if privacy is your priority, ProtonMail is excellent. For business users, Outlook offers great integration.\n\nWhat's most important to you in an email service - privacy, features, or integration?"
            ];
        }

        // Handle vague follow-up questions that need context
        if (isFollowUp && (lowerMessage.includes('what do you think') || lowerMessage.includes('any alternatives') ||
            lowerMessage.includes('opinion') || lowerMessage.includes('it'))) {
            return [
                "I'd love to give you a helpful opinion and suggest alternatives! However, I need a bit more clarity about what specifically you're referring to.\n\nLooking at our conversation, could you help me understand:\n\n🤔 **What exactly are you asking about?**\n- A specific product, service, or concept we discussed?\n- A particular feature or aspect?\n- A decision you're trying to make?\n\n📋 **What are your priorities?**\n- Cost, performance, ease of use?\n- Privacy, reliability, specific features?\n- Integration with other tools?\n\n💭 **What's your goal?**\n- Solving a specific problem?\n- Improving your current setup?\n- Exploring new options?\n\nOnce I understand what you're referring to, I can provide much more targeted thoughts and suggest relevant alternatives that would actually be useful for your situation!"
            ];
        }

        if (lowerMessage.includes('gmail') || lowerMessage.includes('email')) {
            return [
                "**Gmail** is Google's free email service that's become one of the most popular worldwide. Here's what makes it special:\n\n**🎯 Core Features:**\n- **15GB free storage** (shared with Google Drive & Photos)\n- **Powerful search** - find any email instantly\n- **Excellent spam filtering** - keeps junk out effectively\n- **Conversation view** - groups related emails together\n- **Mobile apps** - seamless sync across devices\n\n**🔗 Google Integration:**\n- Works seamlessly with Google Drive, Calendar, Meet\n- Easy file sharing and collaboration\n- Google Workspace integration for businesses\n\n**✨ Smart Features:**\n- **Smart Compose** - AI-powered writing suggestions\n- **Priority Inbox** - highlights important emails\n- **Filters & Labels** - powerful organization tools\n- **Offline access** - read/compose without internet\n\n**🌟 Why People Love It:**\n- Reliable and rarely goes down\n- Clean, intuitive interface\n- Constantly updated with new features\n- Free with generous storage\n\nGmail has essentially set the standard for modern email services!"
            ];
        }

        if (lowerMessage.includes('quantum')) {
            return [
                "Quantum computing is a fascinating field that leverages quantum mechanical phenomena like **superposition** and **entanglement** to process information in ways classical computers cannot.\n\nIn simple terms:\n- Classical bits are either 0 or 1\n- Quantum bits (qubits) can be both 0 and 1 simultaneously\n- This allows quantum computers to explore many solutions at once\n\nQuantum computers excel at specific problems like cryptography, optimization, and simulating molecular behavior, but they're not meant to replace classical computers for everyday tasks."
            ];
        }
        
        if (lowerMessage.includes('story') && lowerMessage.includes('space')) {
            return [
                "**The Last Signal**\n\nCaptain Elena Vasquez floated in the observation deck of the starship *Horizon*, watching the swirling nebula paint the cosmos in shades of purple and gold. After three years of deep space exploration, her crew had discovered something extraordinary—a signal from an ancient civilization.\n\nThe transmission wasn't just data; it was a **map**. A map leading to a network of worlds that had once thrived across the galaxy, connected by technology beyond human understanding.\n\nAs Elena studied the star charts, she realized this wasn't just a discovery—it was an invitation. The universe was far less lonely than they had ever imagined."
            ];
        }
        
        if (lowerMessage.includes('meal') || lowerMessage.includes('healthy')) {
            return [
                "Here's a **balanced weekly meal plan** to get you started:\n\n**Monday:**\n- Breakfast: Greek yogurt with berries and granola\n- Lunch: Quinoa salad with grilled chicken and vegetables\n- Dinner: Baked salmon with roasted sweet potatoes and broccoli\n\n**Tuesday:**\n- Breakfast: Overnight oats with banana and almond butter\n- Lunch: Lentil soup with whole grain bread\n- Dinner: Stir-fried tofu with brown rice and mixed vegetables\n\n**Wednesday:**\n- Breakfast: Smoothie bowl with spinach, mango, and chia seeds\n- Lunch: Turkey and avocado wrap with side salad\n- Dinner: Lean beef with quinoa and roasted Brussels sprouts\n\n*Continue this pattern, focusing on lean proteins, whole grains, plenty of vegetables, and healthy fats!*"
            ];
        }
        
        if (lowerMessage.includes('machine learning') || lowerMessage.includes('ml')) {
            return [
                "**Machine Learning** is a subset of artificial intelligence that enables computers to learn and improve from experience without being explicitly programmed.\n\n**Key Concepts:**\n\n🔹 **Supervised Learning**: Learning from labeled examples (like email spam detection)\n🔹 **Unsupervised Learning**: Finding patterns in unlabeled data (like customer segmentation)\n🔹 **Reinforcement Learning**: Learning through trial and error with rewards/penalties\n\n**Common Applications:**\n- Image recognition\n- Natural language processing\n- Recommendation systems\n- Predictive analytics\n\nThe goal is to create algorithms that can make predictions or decisions based on data patterns, much like how humans learn from experience!"
            ];
        }
        
        // Default responses
        return [
            "That's an interesting question! I'd be happy to help you explore that topic further. Could you provide a bit more context or let me know what specific aspect you'd like to focus on?",
            "I understand what you're asking about. Let me break this down for you in a clear and helpful way. What particular angle or detail would you like me to emphasize?",
            "Great question! There are several ways to approach this. Would you like me to provide a comprehensive overview, or would you prefer to dive deep into a specific aspect?",
            "I'm here to help! That's definitely something I can assist with. To give you the most relevant and useful information, could you tell me a bit more about your specific needs or goals?",
            "Thanks for bringing this up! This is actually a topic I find quite fascinating. Let me share some insights that might be helpful for your situation."
        ];
    }

    setupCleanupHandlers() {
        // Simple cleanup on page hide
        window.onpagehide = () => this.cleanup();
    }

    refreshMessageFormatting() {
        // Re-process all existing messages with updated formatting
        if (!this.chatContainer) return;

        const messageContents = this.chatContainer.querySelectorAll('.message-content');
        messageContents.forEach(content => {
            // Skip if this is a user message (they don't need HTML escaping)
            if (content.closest('.user-message')) return;

            // Get the original text content and re-format it
            const messageElement = content.closest('.message');
            if (messageElement && messageElement.dataset.originalContent) {
                content.innerHTML = this.formatMessage(messageElement.dataset.originalContent);
                this.addCodeBlockCopyButtons(content);
            }
        });
    }

    forceRefreshCurrentChat() {
        // Force refresh the current chat display with updated formatting
        if (this.currentChatId && this.chats.has(this.currentChatId)) {
            const chat = this.chats.get(this.currentChatId);
            this.renderChat(chat);
        }
    }




    handleInputChange() {
        const hasText = this.messageInput.value.trim().length > 0;
        this.sendBtn.disabled = !hasText || this.isTyping;

        // Add typing indicator to input wrapper
        const inputWrapper = document.querySelector('.input-wrapper');
        if (hasText && inputWrapper) {
            inputWrapper.classList.add('typing');
        } else if (inputWrapper) {
            inputWrapper.classList.remove('typing');
        }
    }

    autoResize() {
        this.messageInput.style.height = 'auto';
        const autoHeight = Math.min(this.messageInput.scrollHeight, 120);

        // If input is empty, reset to original size (don't preserve height)
        if (this.messageInput.value.trim() === '') {
            this.messageInput.style.height = 'auto'; // Reset to original size
        } else {
            this.messageInput.style.height = autoHeight + 'px';
            // If we have content and the height is expanded, track it for preservation
            if (autoHeight > 32) {
                this.preservedInputHeight = autoHeight;
            }
        }
    }

    initializePlaceholderRotation() {
        if (!this.messageInput) return;

        // Set initial placeholder
        this.messageInput.placeholder = this.placeholderTexts[0];

        // Rotate placeholder text every 4 seconds when input is empty and not focused
        setInterval(() => {
            if (this.messageInput &&
                this.messageInput.value.trim() === '' &&
                document.activeElement !== this.messageInput) {

                this.currentPlaceholderIndex = (this.currentPlaceholderIndex + 1) % this.placeholderTexts.length;
                this.messageInput.placeholder = this.placeholderTexts[this.currentPlaceholderIndex];
            }
        }, 4000);
    }

    handleChatScroll() {
        if (!this.chatContainer || this.isAutoScrolling) {
            return;
        }

        const { scrollTop, scrollHeight, clientHeight } = this.chatContainer;
        const distanceFromBottom = scrollHeight - scrollTop - clientHeight;

        // Check if user is near the bottom
        const wasScrolledUp = this.userScrolledUp;
        this.userScrolledUp = distanceFromBottom > this.scrollThreshold;

        // If user was scrolled up but now returned to bottom, resume auto-scrolling
        if (wasScrolledUp && !this.userScrolledUp && this.isTyping) {
            console.log('User returned to bottom during typing - resuming auto-scroll');
        }
    }

    scrollToBottom(force = false, smooth = true) {
        if (!this.chatContainer) return;

        // Only auto-scroll if user hasn't scrolled up or if forced
        if (force || !this.userScrolledUp) {
            this.isAutoScrolling = true;

            // Function to perform the actual scroll
            const performScroll = () => {
                // Ensure we scroll to the absolute bottom by adding extra pixels
                const scrollTarget = this.chatContainer.scrollHeight + 100;

                if (smooth) {
                    this.chatContainer.scrollTo({
                        top: scrollTarget,
                        behavior: 'smooth'
                    });
                } else {
                    this.chatContainer.scrollTop = scrollTarget;
                }
            };

            // Perform scroll immediately
            performScroll();

            // For forced scrolls (like after sending a message), ensure we reach the bottom
            // by doing a second scroll after a brief delay to account for DOM updates
            if (force) {
                setTimeout(() => {
                    performScroll();
                }, 50);
            }

            this.userScrolledUp = false;

            // Reset auto-scrolling flag after a brief delay (longer for smooth scroll)
            setTimeout(() => {
                this.isAutoScrolling = false;
            }, smooth ? 300 : 100);
        }
    }

    resetScrollState() {
        // Reset scroll behavior state when starting fresh
        this.userScrolledUp = false;
        this.isAutoScrolling = false;
    }

    checkAPIConfiguration() {
        if (!this.apiConfigured) {
            console.warn('Google AI Studio API key not configured. Using offline mode.');
            this.showAPISetupNotification();
        } else {
            console.log('Google AI Studio API configured successfully.');
        }
    }

    showAPISetupNotification() {
        // Create a notification banner
        const notification = document.createElement('div');
        notification.className = 'api-setup-notification';
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-info-circle"></i>
                <div class="notification-text">
                    <strong>API Setup Required</strong>
                    <p>To enable real AI responses, please add your Google AI Studio API key to config.js.
                    <a href="https://aistudio.google.com/app/apikey" target="_blank">Get your free API key here</a></p>
                </div>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        document.body.insertBefore(notification, document.body.firstChild);

        // Auto-hide after 10 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 10000);
    }

    showNotification(message, type = 'info') {
        // Create a notification toast
        const notification = document.createElement('div');
        notification.className = `notification-toast notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-message">${message}</span>
                <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        // Add to body
        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => notification.classList.add('show'), 100);

        // Auto-hide after 5 seconds
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }, 5000);
    }

    // Custom confirmation dialog
    showCustomConfirmDialog(options) {
        return new Promise((resolve) => {
            // Create dialog overlay
            const overlay = document.createElement('div');
            overlay.className = 'custom-dialog-overlay';

            // Create dialog content
            const dialog = document.createElement('div');
            dialog.className = 'custom-dialog';

            // Add content based on type
            const typeClass = options.type === 'danger' ? 'danger' : 'default';
            dialog.innerHTML = `
                <div class="dialog-header">
                    <div class="dialog-icon ${typeClass}">
                        <i class="fas ${options.type === 'danger' ? 'fa-exclamation-triangle' : 'fa-question-circle'}"></i>
                    </div>
                    <h3 class="dialog-title">${options.title}</h3>
                </div>
                <div class="dialog-content">
                    <p class="dialog-message">${options.message}</p>
                    ${options.description ? `<p class="dialog-description">${options.description}</p>` : ''}
                </div>
                <div class="dialog-actions">
                    <button class="dialog-btn secondary" data-action="cancel">
                        ${options.cancelText || 'Cancel'}
                    </button>
                    <button class="dialog-btn ${typeClass}" data-action="confirm">
                        ${options.confirmText || 'Confirm'}
                    </button>
                </div>
            `;

            overlay.appendChild(dialog);
            document.body.appendChild(overlay);

            // Add event listeners
            const handleAction = (action) => {
                overlay.classList.add('fade-out');
                setTimeout(() => {
                    document.body.removeChild(overlay);
                    resolve(action === 'confirm');
                }, 200);
            };

            // Button clicks
            dialog.addEventListener('click', (e) => {
                const action = e.target.dataset.action;
                if (action) {
                    handleAction(action);
                }
            });

            // Overlay click to cancel
            overlay.addEventListener('click', (e) => {
                if (e.target === overlay) {
                    handleAction('cancel');
                }
            });

            // Escape key to cancel
            const handleKeydown = (e) => {
                if (e.key === 'Escape') {
                    document.removeEventListener('keydown', handleKeydown);
                    handleAction('cancel');
                }
            };
            document.addEventListener('keydown', handleKeydown);

            // Animate in
            setTimeout(() => overlay.classList.add('show'), 10);
        });
    }

    // Custom input dialog for text confirmation
    showCustomInputDialog(options) {
        return new Promise((resolve) => {
            // Create dialog overlay
            const overlay = document.createElement('div');
            overlay.className = 'custom-dialog-overlay';

            // Create dialog content
            const dialog = document.createElement('div');
            dialog.className = 'custom-dialog input-dialog';

            // Add content
            const typeClass = options.type === 'danger' ? 'danger' : 'default';
            dialog.innerHTML = `
                <div class="dialog-header">
                    <div class="dialog-icon ${typeClass}">
                        <i class="fas fa-keyboard"></i>
                    </div>
                    <h3 class="dialog-title">${options.title}</h3>
                </div>
                <div class="dialog-content">
                    <p class="dialog-message">${options.message}</p>
                    ${options.description ? `<p class="dialog-description">${options.description}</p>` : ''}
                    <div class="input-group">
                        <label class="input-label">${options.inputLabel || 'Enter text:'}</label>
                        <input type="text" class="dialog-input" placeholder="${options.placeholder || ''}" autocomplete="off">
                        <div class="input-hint">Expected: <span class="expected-text">${options.expectedValue}</span></div>
                    </div>
                </div>
                <div class="dialog-actions">
                    <button class="dialog-btn secondary" data-action="cancel">
                        ${options.cancelText || 'Cancel'}
                    </button>
                    <button class="dialog-btn ${typeClass}" data-action="confirm" disabled>
                        ${options.confirmText || 'Confirm'}
                    </button>
                </div>
            `;

            overlay.appendChild(dialog);
            document.body.appendChild(overlay);

            const input = dialog.querySelector('.dialog-input');
            const confirmBtn = dialog.querySelector('[data-action="confirm"]');

            // Input validation
            const validateInput = () => {
                const isValid = input.value === options.expectedValue;
                confirmBtn.disabled = !isValid;
                confirmBtn.classList.toggle('enabled', isValid);
                input.classList.toggle('valid', isValid && input.value.length > 0);
                input.classList.toggle('invalid', !isValid && input.value.length > 0);
            };

            input.addEventListener('input', validateInput);
            input.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !confirmBtn.disabled) {
                    handleAction('confirm');
                }
            });

            // Add event listeners
            const handleAction = (action) => {
                overlay.classList.add('fade-out');
                setTimeout(() => {
                    document.body.removeChild(overlay);
                    resolve(action === 'confirm' && input.value === options.expectedValue);
                }, 200);
            };

            // Button clicks
            dialog.addEventListener('click', (e) => {
                const action = e.target.dataset.action;
                if (action) {
                    handleAction(action);
                }
            });

            // Overlay click to cancel
            overlay.addEventListener('click', (e) => {
                if (e.target === overlay) {
                    handleAction('cancel');
                }
            });

            // Escape key to cancel
            const handleKeydown = (e) => {
                if (e.key === 'Escape') {
                    document.removeEventListener('keydown', handleKeydown);
                    handleAction('cancel');
                }
            };
            document.addEventListener('keydown', handleKeydown);

            // Animate in and focus input
            setTimeout(() => {
                overlay.classList.add('show');
                input.focus();
            }, 10);
        });
    }

    // File Upload Methods
    async handleFileUpload(event) {
        const files = Array.from(event.target.files);
        if (files.length === 0) return;

        console.log(`Received ${files.length} file(s) for upload`);

        // Validate files
        const validFiles = [];
        const errors = [];

        for (const file of files) {
            console.log(`Validating file: ${file.name} (${file.type}, ${(file.size / 1024).toFixed(1)}KB)`);
            const validation = this.validateFile(file);
            if (validation.valid) {
                validFiles.push(file);
            } else {
                errors.push(`${file.name}: ${validation.error}`);
            }
        }

        // Show errors if any
        if (errors.length > 0) {
            console.error('File validation errors:', errors);
            this.showNotification(
                `${errors.length} file(s) failed validation. Check console for details.`,
                'error'
            );

            // Also show detailed errors in console for debugging
            errors.forEach(error => console.error('Validation error:', error));
        }

        // Process valid files
        if (validFiles.length > 0) {
            console.log(`Processing ${validFiles.length} valid file(s)`);
            await this.processFiles(validFiles);
        } else if (errors.length > 0) {
            // No valid files and there were errors
            this.showNotification('No valid files to process', 'warning');
        }

        // Clear file input
        event.target.value = '';
    }

    validateFile(file) {
        // Check if file exists
        if (!file) {
            return {
                valid: false,
                error: 'No file provided'
            };
        }

        // Check file type
        if (!this.supportedFileTypes[file.type]) {
            const supportedTypes = Object.values(this.supportedFileTypes).join(', ');
            return {
                valid: false,
                error: `Unsupported file type "${file.type}". Supported formats: ${supportedTypes}`
            };
        }

        // Check file size
        if (file.size === 0) {
            return {
                valid: false,
                error: 'File is empty'
            };
        }

        if (file.size > this.maxFileSize) {
            return {
                valid: false,
                error: `File too large (${(file.size / (1024 * 1024)).toFixed(1)}MB). Maximum size: ${this.maxFileSize / (1024 * 1024)}MB`
            };
        }

        // Check file name
        if (!file.name || file.name.trim() === '') {
            return {
                valid: false,
                error: 'File has no name'
            };
        }

        return { valid: true };
    }

    async processFiles(files) {
        // Add visual feedback
        if (this.uploadBtn) {
            this.uploadBtn.classList.add('processing');
            this.uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        }

        console.log(`Processing ${files.length} file(s)...`);

        // Show processing notification
        this.showNotification(`Processing ${files.length} document(s)...`, 'info');

        const extractedTexts = [];
        const uploadedFiles = [];
        const processingErrors = [];

        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            try {
                // Update progress feedback
                if (this.uploadBtn) {
                    this.uploadBtn.innerHTML = `<i class="fas fa-spinner fa-spin"></i> ${i + 1}/${files.length}`;
                }

                console.log(`Processing file ${i + 1}/${files.length}: ${file.name}`);

                // Extract text from file
                const text = await this.extractTextFromFile(file);

                if (text && text.trim().length > 0) {
                    console.log(`Successfully extracted ${text.length} characters from ${file.name}`);

                    // Upload file to Supabase storage if available (optional)
                    let fileUrl = null;
                    if (this.useDatabase) {
                        try {
                            fileUrl = await this.uploadFileToStorage(file);
                            console.log(`Successfully uploaded ${file.name} to storage`);
                        } catch (uploadError) {
                            console.warn(`Failed to upload ${file.name} to storage (continuing without storage):`, uploadError);
                            // Continue processing even if storage upload fails
                        }
                    }

                    extractedTexts.push({
                        filename: file.name,
                        type: this.supportedFileTypes[file.type],
                        content: text,
                        fileUrl: fileUrl,
                        size: file.size,
                        wordCount: text.split(/\s+/).length,
                        characterCount: text.length
                    });

                    uploadedFiles.push({
                        filename: file.name,
                        url: fileUrl,
                        size: file.size,
                        type: file.type
                    });
                } else {
                    console.warn(`No text extracted from ${file.name}`);
                    processingErrors.push(`${file.name}: No readable text content found`);
                }
            } catch (error) {
                console.error(`Error processing ${file.name}:`, error);
                processingErrors.push(`${file.name}: ${error.message}`);
            }
        }

        // Reset upload button state
        if (this.uploadBtn) {
            this.uploadBtn.classList.remove('processing');
            this.uploadBtn.innerHTML = '<i class="fas fa-paperclip"></i>';
        }

        // Show results
        if (extractedTexts.length > 0) {
            const totalChars = extractedTexts.reduce((sum, doc) => sum + doc.characterCount, 0);
            const totalWords = extractedTexts.reduce((sum, doc) => sum + doc.wordCount, 0);

            console.log(`Successfully processed ${extractedTexts.length}/${files.length} documents`);
            console.log(`Total content: ${totalChars} characters, ${totalWords} words`);

            this.showNotification(
                `Successfully processed ${extractedTexts.length} document(s) - ${totalWords} words extracted`,
                'success'
            );

            await this.handleExtractedDocuments(extractedTexts, uploadedFiles);
        } else {
            console.warn('No text could be extracted from any uploaded files.');
            this.showNotification('No readable text found in uploaded documents', 'error');
        }

        // Show processing errors if any
        if (processingErrors.length > 0) {
            console.warn('Processing errors:', processingErrors);
            this.showNotification(
                `Some files had issues: ${processingErrors.length} error(s)`,
                'warning'
            );
        }
    }

    async uploadFileToStorage(file) {
        if (!this.useDatabase) {
            throw new Error('Supabase not available');
        }

        try {
            // Generate unique filename with timestamp
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const userId = await this.getCurrentUserId();
            const filename = `${userId || 'anonymous'}/${timestamp}_${file.name}`;

            // Upload file to Supabase storage
            const { error } = await this.supabaseService.supabase.storage
                .from('documents')
                .upload(filename, file, {
                    cacheControl: '3600',
                    upsert: false
                });

            if (error) {
                throw error;
            }

            // Get public URL
            const { data: urlData } = this.supabaseService.supabase.storage
                .from('documents')
                .getPublicUrl(filename);

            console.log(`File uploaded successfully: ${filename}`);
            return urlData.publicUrl;

        } catch (error) {
            console.error('Error uploading file to storage:', error);
            throw error;
        }
    }

    async extractTextFromFile(file) {
        const fileType = file.type;

        switch (fileType) {
            case 'application/pdf':
                return await this.extractTextFromPDF(file);

            case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
            case 'application/msword':
                return await this.extractTextFromWord(file);

            case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
            case 'application/vnd.ms-excel':
                return await this.extractTextFromExcel(file);

            case 'text/plain':
            case 'text/csv':
            case 'application/json':
                return await this.extractTextFromPlainText(file);

            default:
                throw new Error(`Unsupported file type: ${fileType}`);
        }
    }

    async extractTextFromPDF(file) {
        const cleanTextFunction = this.cleanExtractedText.bind(this);
        return new Promise((resolve, reject) => {
            const fileReader = new FileReader();
            fileReader.onload = async function() {
                try {
                    const typedArray = new Uint8Array(this.result);
                    const pdf = await pdfjsLib.getDocument(typedArray).promise;
                    let fullText = '';
                    let totalPages = pdf.numPages;

                    console.log(`Processing PDF: ${file.name} (${totalPages} pages)`);

                    for (let i = 1; i <= totalPages; i++) {
                        const page = await pdf.getPage(i);
                        const textContent = await page.getTextContent();

                        // Improved text extraction with better spacing and formatting
                        let pageText = '';
                        let lastY = null;

                        textContent.items.forEach((item, index) => {
                            // Add line breaks for new lines (when Y position changes significantly)
                            if (lastY !== null && Math.abs(lastY - item.transform[5]) > 5) {
                                pageText += '\n';
                            }

                            // Add the text with proper spacing
                            if (index > 0 && !pageText.endsWith(' ') && !pageText.endsWith('\n')) {
                                pageText += ' ';
                            }
                            pageText += item.str;
                            lastY = item.transform[5];
                        });

                        fullText += `\n--- Page ${i} ---\n${pageText}\n`;
                    }

                    // Clean up the extracted text
                    fullText = cleanTextFunction(fullText);
                    console.log(`PDF extraction complete: ${fullText.length} characters extracted`);
                    resolve(fullText);
                } catch (error) {
                    console.error('PDF extraction error:', error);
                    reject(new Error(`Failed to extract text from PDF: ${error.message}`));
                }
            };
            fileReader.onerror = () => reject(new Error('Failed to read PDF file'));
            fileReader.readAsArrayBuffer(file);
        });
    }

    async extractTextFromWord(file) {
        const cleanTextFunction = this.cleanExtractedText.bind(this);
        return new Promise((resolve, reject) => {
            const fileReader = new FileReader();
            fileReader.onload = async function() {
                try {
                    const arrayBuffer = this.result;
                    console.log(`Processing Word document: ${file.name}`);

                    // Extract both raw text and HTML to get better formatting
                    const textResult = await mammoth.extractRawText({ arrayBuffer });
                    const htmlResult = await mammoth.convertToHtml({ arrayBuffer });

                    let extractedText = textResult.value;

                    // If raw text is too short, try to extract from HTML
                    if (extractedText.length < 100 && htmlResult.value) {
                        // Convert HTML to plain text while preserving structure
                        const tempDiv = document.createElement('div');
                        tempDiv.innerHTML = htmlResult.value;
                        extractedText = tempDiv.textContent || tempDiv.innerText || extractedText;
                    }

                    // Clean up the extracted text
                    extractedText = cleanTextFunction(extractedText);
                    console.log(`Word extraction complete: ${extractedText.length} characters extracted`);
                    resolve(extractedText);
                } catch (error) {
                    console.error('Word extraction error:', error);
                    reject(new Error(`Failed to extract text from Word document: ${error.message}`));
                }
            };
            fileReader.onerror = () => reject(new Error('Failed to read Word file'));
            fileReader.readAsArrayBuffer(file);
        });
    }

    async extractTextFromExcel(file) {
        const cleanTextFunction = this.cleanExtractedText.bind(this);
        return new Promise((resolve, reject) => {
            const fileReader = new FileReader();
            fileReader.onload = function() {
                try {
                    const data = new Uint8Array(this.result);
                    const workbook = XLSX.read(data, { type: 'array' });
                    let fullText = '';

                    console.log(`Processing Excel: ${file.name} (${workbook.SheetNames.length} sheets)`);

                    workbook.SheetNames.forEach((sheetName, index) => {
                        const worksheet = workbook.Sheets[sheetName];

                        // Get both CSV format (for structured data) and JSON format (for better data representation)
                        const csvText = XLSX.utils.sheet_to_csv(worksheet);
                        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, defval: '' });

                        fullText += `\n--- Sheet ${index + 1}: ${sheetName} ---\n`;

                        if (jsonData.length > 0) {
                            // Convert to a more readable table format
                            const maxCols = Math.max(...jsonData.map(row => row.length));

                            jsonData.forEach((row, rowIndex) => {
                                if (rowIndex === 0 && row.some(cell => cell && cell.toString().trim())) {
                                    // Header row
                                    fullText += `Headers: ${row.join(' | ')}\n`;
                                    fullText += `${'---'.repeat(maxCols)}\n`;
                                } else if (row.some(cell => cell && cell.toString().trim())) {
                                    // Data row (only include if it has content)
                                    fullText += `Row ${rowIndex + 1}: ${row.join(' | ')}\n`;
                                }
                            });
                        } else {
                            // Fallback to CSV if JSON parsing fails
                            fullText += csvText;
                        }

                        fullText += '\n';
                    });

                    // Clean up the extracted text
                    fullText = cleanTextFunction(fullText);
                    console.log(`Excel extraction complete: ${fullText.length} characters extracted`);
                    resolve(fullText);
                } catch (error) {
                    console.error('Excel extraction error:', error);
                    reject(new Error(`Failed to extract text from Excel file: ${error.message}`));
                }
            };
            fileReader.onerror = () => reject(new Error('Failed to read Excel file'));
            fileReader.readAsArrayBuffer(file);
        });
    }

    async extractTextFromPlainText(file) {
        const cleanTextFunction = this.cleanExtractedText.bind(this);
        return new Promise((resolve, reject) => {
            const fileReader = new FileReader();
            fileReader.onload = function() {
                try {
                    let text = this.result;
                    console.log(`Processing text file: ${file.name}`);

                    // Handle different text file types
                    if (file.type === 'text/csv') {
                        // For CSV files, try to format them better
                        const lines = text.split('\n');
                        if (lines.length > 1) {
                            text = `CSV Data from ${file.name}:\n\n`;
                            lines.forEach((line, index) => {
                                if (line.trim()) {
                                    if (index === 0) {
                                        text += `Headers: ${line}\n`;
                                        text += `${'---'.repeat(10)}\n`;
                                    } else {
                                        text += `Row ${index}: ${line}\n`;
                                    }
                                }
                            });
                        }
                    } else if (file.type === 'application/json') {
                        // For JSON files, try to format them
                        try {
                            const jsonData = JSON.parse(text);
                            text = `JSON Data from ${file.name}:\n\n${JSON.stringify(jsonData, null, 2)}`;
                        } catch (e) {
                            // If JSON parsing fails, use raw text
                            text = `JSON File Content from ${file.name}:\n\n${text}`;
                        }
                    }

                    // Clean up the extracted text
                    text = cleanTextFunction(text);
                    console.log(`Text extraction complete: ${text.length} characters extracted`);
                    resolve(text);
                } catch (error) {
                    console.error('Text extraction error:', error);
                    reject(new Error(`Failed to process text file: ${error.message}`));
                }
            };
            fileReader.onerror = () => reject(new Error('Failed to read text file'));
            fileReader.readAsText(file, 'UTF-8');
        });
    }

    // Clean and normalize extracted text
    cleanExtractedText(text) {
        if (!text || typeof text !== 'string') {
            return '';
        }

        // Remove excessive whitespace and normalize line breaks
        text = text
            .replace(/\r\n/g, '\n')  // Normalize line endings
            .replace(/\r/g, '\n')    // Handle old Mac line endings
            .replace(/\n{3,}/g, '\n\n')  // Reduce multiple line breaks to max 2
            .replace(/[ \t]+/g, ' ')  // Replace multiple spaces/tabs with single space
            .replace(/[ \t]*\n[ \t]*/g, '\n')  // Remove spaces around line breaks
            .trim();  // Remove leading/trailing whitespace

        // Remove common document artifacts
        text = text
            .replace(/\f/g, '\n')  // Replace form feeds with line breaks
            .replace(/[\u0000-\u0008\u000B\u000C\u000E-\u001F\u007F]/g, '')  // Remove control characters
            .replace(/\u00A0/g, ' ')  // Replace non-breaking spaces with regular spaces
            .replace(/\u2028/g, '\n')  // Replace line separator with line break
            .replace(/\u2029/g, '\n\n');  // Replace paragraph separator with double line break

        // Ensure the text isn't empty after cleaning
        if (!text.trim()) {
            return '';
        }

        return text;
    }

    async handleExtractedDocuments(extractedTexts, uploadedFiles = []) {
        // Store the document data for later use
        this.pendingDocuments = {
            extractedTexts: extractedTexts,
            uploadedFiles: uploadedFiles,
            timestamp: new Date().toISOString()
        };



        // Ensure we have a current chat
        if (!this.currentChatId) {
            await this.createNewChat();
        }

        // Store document metadata in the current chat if using database
        if (this.useDatabase && this.currentChatId && uploadedFiles.length > 0) {
            try {
                const chat = this.chats.get(this.currentChatId);
                if (chat && chat.isFromDatabase && this.supabaseService.updateConversation) {
                    // Add document metadata to conversation metadata
                    const conversationMetadata = {
                        uploadedDocuments: uploadedFiles,
                        uploadTimestamp: new Date().toISOString()
                    };

                    await this.supabaseService.updateConversation(this.currentChatId, {
                        metadata: conversationMetadata
                    });
                    console.log('Document metadata stored in conversation');
                }
            } catch (error) {
                console.warn('Failed to store document metadata (continuing without metadata):', error);
            }
        }

        // Show success notification with document summary
        const successfulUploads = uploadedFiles.filter(file => file.url).length;
        const storageMsg = successfulUploads > 0 ? ` ${successfulUploads} file(s) stored in cloud.` : '';
        console.log(`Successfully processed ${extractedTexts.length} document(s).${storageMsg} Ready for your questions!`);

        // Create a document preview in the chat
        const documentPreview = document.createElement('div');
        documentPreview.className = 'document-preview-enhanced';

        // Calculate total statistics
        const totalWords = extractedTexts.reduce((sum, doc) => sum + (doc.wordCount || 0), 0);
        const totalChars = extractedTexts.reduce((sum, doc) => sum + (doc.characterCount || 0), 0);

        if (extractedTexts.length === 1) {
            // Single document display
            const doc = extractedTexts[0];
            documentPreview.innerHTML = `
                <div class="single-document-display">
                    <i class="fas fa-file-alt"></i>
                    <span class="document-text">Document Uploaded: <strong>${doc.filename}</strong></span>
                    <button class="clear-documents-btn" onclick="window.clearPendingDocuments()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
        } else {
            // Multiple documents expandable display
            const documentList = extractedTexts.map(doc => {
                const sizeStr = doc.size ? ` (${(doc.size / 1024).toFixed(1)}KB)` : '';
                const wordStr = doc.wordCount ? ` - ${doc.wordCount} words` : '';
                const urlStr = doc.fileUrl ? ' 🔗' : '';
                return `<div class="document-item-enhanced">
                    <i class="fas fa-file-alt"></i>
                    <span class="doc-name">${doc.filename}</span>
                    <span class="doc-type">${doc.type}</span>
                    <span class="doc-stats">${sizeStr}${wordStr}${urlStr}</span>
                </div>`;
            }).join('');

            documentPreview.innerHTML = `
                <div class="multiple-documents-display">
                    <div class="documents-header" onclick="this.parentElement.classList.toggle('expanded')">
                        <div class="header-content">
                            <i class="fas fa-file-alt"></i>
                            <span class="document-text">${extractedTexts.length} Documents Uploaded</span>
                        </div>
                        <div class="header-controls">
                            <i class="fas fa-chevron-up expand-arrow"></i>
                            <button class="clear-documents-btn" onclick="event.stopPropagation(); window.clearPendingDocuments()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="documents-content">
                        ${documentList}
                        <div class="document-stats-enhanced">
                            <span><i class="fas fa-chart-bar"></i> Total: ${totalWords} words, ${(totalChars / 1024).toFixed(1)}KB text</span>
                        </div>
                    </div>
                </div>
            `;
        }
        if (this.chatContainer) {
            // Insert the document preview in a container that matches the input area
            const previewContainer = document.createElement('div');
            previewContainer.className = 'document-preview-container';
            previewContainer.appendChild(documentPreview);
            this.chatContainer.appendChild(previewContainer);
            this.scrollToBottom();
        }

        // Update input placeholder
        if (this.messageInput) {
            this.messageInput.placeholder = `Ask questions about your ${extractedTexts.length} uploaded document(s)...`;
            this.messageInput.focus();
        }

        // Keep upload button as default (no document count display)
        if (this.uploadBtn) {
            this.uploadBtn.innerHTML = '<i class="fas fa-paperclip"></i>';
            // Remove the has-documents class since we're not showing count
            this.uploadBtn.classList.remove('has-documents');
        }
    }

    clearPendingDocuments() {
        this.pendingDocuments = null;

        // Reset input placeholder
        if (this.messageInput) {
            this.messageInput.placeholder = 'Ask me anything... ✨';
        }

        // Reset upload button
        if (this.uploadBtn) {
            this.uploadBtn.innerHTML = '<i class="fas fa-paperclip"></i>';
        }

        // Remove document preview from chat (the temporary preview, not the persistent analysis)
        const documentPreview = this.chatContainer.querySelector('.document-preview-enhanced');
        if (documentPreview) {
            documentPreview.remove();
        }

        // Also remove the container if it exists
        const documentPreviewContainer = this.chatContainer.querySelector('.document-preview-container');
        if (documentPreviewContainer) {
            documentPreviewContainer.remove();
        }

        console.log('Cleared pending documents');
    }
}

// Loading Manager
class LoadingManager {
    constructor() {
        this.loadingContainer = document.getElementById('loadingContainer');
        this.loadingText = document.getElementById('loadingText');
        this.progressFill = document.getElementById('progressFill');
        this.progressText = document.getElementById('progressText');
        this.currentProgress = 0;
        this.loadingSteps = [
            { text: "Authenticating user...", duration: 800 },
            { text: "Initializing chat interface...", duration: 600 },
            { text: "Loading conversation history...", duration: 1000 },
            { text: "Setting up AI connection...", duration: 500 },
            { text: "Finalizing setup...", duration: 400 }
        ];
    }

    show() {
        this.loadingContainer.style.display = 'flex';
        this.loadingContainer.classList.remove('fade-out');
        this.currentProgress = 0;
        this.updateProgress(0);
    }

    hide() {
        this.loadingContainer.classList.add('fade-out');
        setTimeout(() => {
            this.loadingContainer.style.display = 'none';
            this.loadingContainer.classList.remove('fade-out');
        }, 500);
    }

    updateProgress(percentage) {
        this.currentProgress = Math.min(100, Math.max(0, percentage));
        this.progressFill.style.width = `${this.currentProgress}%`;
        this.progressText.textContent = `${Math.round(this.currentProgress)}%`;
    }

    updateText(text) {
        this.loadingText.textContent = text;
    }

    async simulateLoading(onComplete) {
        let totalDuration = this.loadingSteps.reduce((sum, step) => sum + step.duration, 0);
        let currentTime = 0;

        for (let i = 0; i < this.loadingSteps.length; i++) {
            const step = this.loadingSteps[i];
            this.updateText(step.text);

            // Animate progress for this step
            const startProgress = (currentTime / totalDuration) * 100;
            const endProgress = ((currentTime + step.duration) / totalDuration) * 100;

            await this.animateProgress(startProgress, endProgress, step.duration);
            currentTime += step.duration;
        }

        // Complete the loading
        this.updateText("Ready!");
        this.updateProgress(100);

        // Wait a bit before calling completion
        await new Promise(resolve => setTimeout(resolve, 300));

        if (onComplete) {
            await onComplete();
        }
    }

    async animateProgress(start, end, duration) {
        return new Promise(resolve => {
            const startTime = Date.now();
            const progressDiff = end - start;

            const animate = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);

                // Easing function for smooth animation
                const easeProgress = 1 - Math.pow(1 - progress, 3);
                const currentProgress = start + (progressDiff * easeProgress);

                this.updateProgress(currentProgress);

                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    resolve();
                }
            };

            requestAnimationFrame(animate);
        });
    }

    // Cleanup method for ChatApp
    cleanup() {
        try {
            // Remove global click handler to prevent memory leaks and duplicate events
            if (this.globalClickHandler) {
                document.removeEventListener('click', this.globalClickHandler);
                this.globalClickHandler = null;
            }
            console.log('ChatApp cleanup completed');
        } catch (error) {
            console.warn('Error during ChatApp cleanup:', error);
        }
    }
}

// Authentication Manager
class AuthManager {
    constructor() {
        this.authContainer = document.getElementById('authContainer');
        this.appContainer = document.getElementById('appContainer');
        this.loginTab = document.getElementById('loginTab');
        this.registerTab = document.getElementById('registerTab');
        this.loginForm = document.getElementById('loginForm');
        this.registerForm = document.getElementById('registerForm');
        this.supabaseService = SupabaseService.getInstance();
        this.loadingManager = new LoadingManager();
        this.appInitialized = false; // Track if app has been initialized
        this.isFirstLoad = true; // Track if this is the first load

        this.bindAuthEvents();
        this.checkPreviousSession();
        this.checkAuthState();
    }



    checkPreviousSession() {
        const wasInitialized = sessionStorage.getItem('veritasAppInitialized') === 'true';
        if (wasInitialized) {
            this.appInitialized = true;
            this.isFirstLoad = false;
        }
    }

    bindAuthEvents() {
        // New streamlined auth event listeners
        this.setupTabSwitching();
        this.setupFormHandlers();
    }

    setupTabSwitching() {
        if (this.loginTab) {
            this.loginTab.onclick = () => this.switchTab('login');
        }

        if (this.registerTab) {
            this.registerTab.onclick = () => this.switchTab('register');
        }
    }

    setupFormHandlers() {
        if (this.loginForm) {
            this.loginForm.onsubmit = (event) => {
                event.preventDefault();
                this.handleLogin();
            };
        }

        if (this.registerForm) {
            this.registerForm.onsubmit = (event) => {
                event.preventDefault();
                this.handleRegister();
            };
        }
    }

    switchTab(tab) {
        if (tab === 'login') {
            this.loginTab.classList.add('active');
            this.registerTab.classList.remove('active');
            this.loginForm.classList.remove('hidden');
            this.registerForm.classList.add('hidden');
        } else {
            this.registerTab.classList.add('active');
            this.loginTab.classList.remove('active');
            this.registerForm.classList.remove('hidden');
            this.loginForm.classList.add('hidden');
        }
    }

    async handleLogin() {
        const email = document.getElementById('loginEmail').value;
        const password = document.getElementById('loginPassword').value;

        if (!email || !password) {
            this.showError('Please fill in all fields');
            return;
        }

        try {
            if (this.supabaseService.isAvailable()) {
                // Use Supabase authentication
                const { data, error } = await this.supabaseService.supabase.auth.signInWithPassword({
                    email: email,
                    password: password
                });

                if (error) {
                    this.showError(error.message);
                    return;
                }

                if (data.user) {
                    await this.showApp();
                }
            } else {
                // Fallback to localStorage-based auth for offline mode
                const users = JSON.parse(localStorage.getItem('users') || '[]');
                const user = users.find(u => u.email === email && u.password === password);

                if (user) {
                    localStorage.setItem('currentUser', JSON.stringify(user));
                    await this.showApp();
                } else {
                    this.showError('Invalid email or password');
                }
            }
        } catch (error) {
            console.error('Login error:', error);
            this.showError('Login failed. Please try again.');
        }
    }

    async handleRegister() {
        const email = document.getElementById('registerEmail').value;
        const password = document.getElementById('registerPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;

        if (!email || !password || !confirmPassword) {
            this.showError('Please fill in all fields');
            return;
        }

        if (password !== confirmPassword) {
            this.showError('Passwords do not match');
            return;
        }

        if (password.length < 6) {
            this.showError('Password must be at least 6 characters long');
            return;
        }

        try {
            if (this.supabaseService.isAvailable()) {
                // Use Supabase authentication
                const { data, error } = await this.supabaseService.supabase.auth.signUp({
                    email: email,
                    password: password
                });

                if (error) {
                    this.showError(error.message);
                    return;
                }

                if (data.user) {
                    // Check if email confirmation is required
                    if (!data.session) {
                        this.showError('Please check your email to confirm your account before logging in.');
                        this.switchTab('login');
                    } else {
                        await this.showApp();
                    }
                }
            } else {
                // Fallback to localStorage for offline mode
                const users = JSON.parse(localStorage.getItem('users') || '[]');

                if (users.find(u => u.email === email)) {
                    this.showError('An account with this email already exists');
                    return;
                }

                const newUser = {
                    id: Date.now().toString(),
                    email: email,
                    password: password, // In real app, this would be hashed
                    createdAt: new Date().toISOString()
                };

                users.push(newUser);
                localStorage.setItem('users', JSON.stringify(users));
                localStorage.setItem('currentUser', JSON.stringify(newUser));

                await this.showApp();
            }
        } catch (error) {
            console.error('Registration error:', error);
            this.showError('Registration failed. Please try again.');
        }
    }

    async checkAuthState() {
        if (this.supabaseService.isAvailable()) {
            // Check Supabase auth state
            const { data: { session } } = await this.supabaseService.supabase.auth.getSession();
            if (session) {
                await this.showApp();
            } else {
                this.showAuth();
            }

            // New auth state monitoring
            this.monitorAuthState();
        } else {
            // Fallback to localStorage check
            const currentUser = localStorage.getItem('currentUser');
            if (currentUser) {
                await this.showApp();
            } else {
                this.showAuth();
            }
        }
    }

    async showApp() {
        // Hide auth container
        this.authContainer.style.display = 'none';

        // Check if this is the first load or if app is already initialized
        const shouldShowLoading = this.isFirstLoad && !this.appInitialized;

        if (shouldShowLoading) {
            // Show loading screen for first-time initialization
            this.loadingManager.show();

            // Start the loading simulation
            await this.loadingManager.simulateLoading(async () => {
                await this.initializeApp();
            });
        } else {
            // Skip loading animation for subsequent visits
            await this.initializeApp();
        }
    }

    async initializeApp() {
        // Update user info in sidebar
        let currentUser = null;

        if (this.supabaseService.isAvailable()) {
            // Get user from Supabase
            const { data: { user } } = await this.supabaseService.supabase.auth.getUser();
            currentUser = user;
        } else {
            // Fallback to localStorage
            const localUser = localStorage.getItem('currentUser');
            if (localUser) {
                currentUser = JSON.parse(localUser);
            }
        }

        if (currentUser) {
            // Update settings menu with user email
            const settingsUserEmail = document.getElementById('settingsUserEmail');
            if (settingsUserEmail) {
                settingsUserEmail.textContent = currentUser.email;
            }
        }

        // Bind profile settings button (only once)
        const profileSettingsBtn = document.getElementById('profileSettingsBtn');
        if (profileSettingsBtn && !profileSettingsBtn.hasAttribute('data-bound')) {
            profileSettingsBtn.addEventListener('click', () => {
                this.showSettingsMenu();
            });
            profileSettingsBtn.setAttribute('data-bound', 'true');
        }

        // Bind logout button (only once)
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn && !logoutBtn.hasAttribute('data-bound')) {
            logoutBtn.addEventListener('click', () => {
                this.logout();
            });
            logoutBtn.setAttribute('data-bound', 'true');
        }

        // Initialize settings menu functionality
        this.initializeSettingsMenu();

        // Initialize the chat app if not already done
        if (!window.chatApp) {
            window.chatApp = new ChatApp();
            globalChatApp = window.chatApp; // Set global reference for cleanup

            // Make grounding function globally available for multi-agent system
            window.callGeminiAPIWithGrounding = (prompt, context) => {
                return window.chatApp.generateAIResponseWithGrounding(prompt, context.conversationHistory || [], '');
            };

            // Make test function globally available for debugging
            window.testLoadingRotation = () => {
                return window.chatApp.testLoadingRotation();
            };
            // Ensure conversations are loaded after authentication is fully established
            await window.chatApp.ensureConversationsLoaded((text, progress) => {
                if (this.isFirstLoad) {
                    this.loadingManager.updateText(text);
                    this.loadingManager.updateProgress(progress);
                }
            });
        } else if (!this.appInitialized) {
            // Only reload conversations if app is not yet initialized (e.g., user just logged in)
            // Don't reload on subsequent auth state changes (like token refresh on tab focus)
            await window.chatApp.reloadConversations();
        }

        // Mark app as initialized
        this.appInitialized = true;
        this.isFirstLoad = false;
        sessionStorage.setItem('veritasAppInitialized', 'true');

        // Show the app and hide loading screen
        this.appContainer.style.display = 'flex';
        if (this.loadingManager.loadingContainer.style.display !== 'none') {
            this.loadingManager.hide();
        }

        // Initialize swipe gestures after app is shown
        setTimeout(() => {
            if (window.chatApp && typeof window.chatApp.initializeSwipeGestures === 'function') {
                window.chatApp.initializeSwipeGestures();
            }
        }, 100);
    }

    monitorAuthState() {
        if (this.supabaseService.isAvailable()) {
            // Modern auth state monitoring with callback
            this.supabaseService.supabase.auth.onAuthStateChange((event, session) => {
                this.handleAuthStateChange(event, session);
            });
        }
    }

    handleAuthStateChange(event, session) {
        switch(event) {
            case 'SIGNED_IN':
                if (session) {
                    // Only show app if not already shown to prevent unnecessary reloads
                    if (this.authContainer.style.display !== 'none') {
                        this.showApp();
                    }
                    // Don't reload conversations if app is already initialized and running
                    // This prevents reloads when Supabase refreshes tokens on tab focus
                }
                break;
            case 'SIGNED_OUT':
                this.showAuth();
                this.resetAppState();
                break;
        }
    }

    resetAppState() {
        this.appInitialized = false;
        this.isFirstLoad = true;
        if (window.chatApp) {
            window.chatApp = null;
        }
    }

    showAuth() {
        this.authContainer.style.display = 'flex';
        this.appContainer.style.display = 'none';
        this.loadingManager.hide();
    }

    showError(message) {
        // Create a simple error notification
        const existingError = document.querySelector('.auth-error');
        if (existingError) {
            existingError.remove();
        }

        const errorDiv = document.createElement('div');
        errorDiv.className = 'auth-error';
        errorDiv.textContent = message;

        const activeForm = document.querySelector('.auth-form:not(.hidden)');
        activeForm.insertBefore(errorDiv, activeForm.firstChild);

        setTimeout(() => {
            if (errorDiv.parentElement) {
                errorDiv.remove();
            }
        }, 5000);
    }

    async logout() {
        // Show loading screen briefly during logout
        this.appContainer.style.display = 'none';
        this.loadingManager.show();
        this.loadingManager.updateText('Signing out...');
        this.loadingManager.updateProgress(50);

        if (this.supabaseService.isAvailable()) {
            // Sign out from Supabase
            await this.supabaseService.supabase.auth.signOut();
        } else {
            // Remove localStorage data for offline mode
            localStorage.removeItem('currentUser');
        }

        // Reset app state for next login
        this.appInitialized = false;
        this.isFirstLoad = true;
        sessionStorage.removeItem('veritasAppInitialized');

        // Small delay for smooth transition
        await new Promise(resolve => setTimeout(resolve, 500));

        this.showAuth();
        // Reset forms
        this.loginForm.reset();
        this.registerForm.reset();
        this.switchTab('login');
    }

    // Settings Menu Methods
    initializeSettingsMenu() {
        // Bind settings menu close button
        const settingsCloseBtn = document.getElementById('settingsCloseBtn');
        if (settingsCloseBtn && !settingsCloseBtn.hasAttribute('data-bound')) {
            settingsCloseBtn.addEventListener('click', () => {
                this.hideSettingsMenu();
            });
            settingsCloseBtn.setAttribute('data-bound', 'true');
        }

        // Bind settings menu overlay click to close
        const settingsMenu = document.getElementById('settingsMenu');
        if (settingsMenu && !settingsMenu.hasAttribute('data-bound')) {
            settingsMenu.addEventListener('click', (e) => {
                if (e.target === settingsMenu) {
                    this.hideSettingsMenu();
                }
            });
            settingsMenu.setAttribute('data-bound', 'true');
        }

        // Bind save settings button
        const saveSettingsBtn = document.getElementById('saveSettingsBtn');
        if (saveSettingsBtn && !saveSettingsBtn.hasAttribute('data-bound')) {
            saveSettingsBtn.addEventListener('click', () => {
                this.saveSettings();
            });
            saveSettingsBtn.setAttribute('data-bound', 'true');
        }

        // Bind reset settings button
        const resetSettingsBtn = document.getElementById('resetSettingsBtn');
        if (resetSettingsBtn && !resetSettingsBtn.hasAttribute('data-bound')) {
            resetSettingsBtn.addEventListener('click', () => {
                this.resetSettings();
            });
            resetSettingsBtn.setAttribute('data-bound', 'true');
        }

        // Bind delete all chats button
        const deleteAllChatsBtn = document.getElementById('deleteAllChatsBtn');
        if (deleteAllChatsBtn && !deleteAllChatsBtn.hasAttribute('data-bound')) {
            deleteAllChatsBtn.addEventListener('click', () => {
                this.deleteAllChatHistory();
            });
            deleteAllChatsBtn.setAttribute('data-bound', 'true');
        }



        // Load saved settings
        this.loadSettings();
    }

    showSettingsMenu() {
        const settingsMenu = document.getElementById('settingsMenu');
        if (settingsMenu) {
            settingsMenu.classList.add('show');
            // Load current settings
            this.loadSettings();
        }
    }

    hideSettingsMenu() {
        const settingsMenu = document.getElementById('settingsMenu');
        if (settingsMenu) {
            settingsMenu.classList.remove('show');
        }
    }

    loadSettings() {
        // Load settings from localStorage
        const settings = JSON.parse(localStorage.getItem('veritasSettings') || '{}');

        // Apply auto-scroll setting
        const autoScrollToggle = document.getElementById('autoScrollToggle');
        if (autoScrollToggle) {
            autoScrollToggle.checked = settings.autoScroll !== false; // default to true
        }
    }

    saveSettings() {
        const autoScrollToggle = document.getElementById('autoScrollToggle');

        const settings = {
            autoScroll: autoScrollToggle ? autoScrollToggle.checked : true
        };

        // Save to localStorage
        localStorage.setItem('veritasSettings', JSON.stringify(settings));

        // Show success message
        this.showSuccessMessage('Settings saved successfully!');

        // Hide settings menu
        this.hideSettingsMenu();
    }

    resetSettings() {
        // Reset to default settings
        const defaultSettings = {
            autoScroll: true
        };

        localStorage.setItem('veritasSettings', JSON.stringify(defaultSettings));

        // Reload settings in UI
        this.loadSettings();

        this.showSuccessMessage('Settings reset to default!');
    }



    showSuccessMessage(message) {
        // Create a temporary success message
        const successDiv = document.createElement('div');
        successDiv.className = 'success-message';
        successDiv.innerHTML = `
            <i class="fas fa-check-circle"></i>
            <span>${message}</span>
        `;

        document.body.appendChild(successDiv);

        // Remove after 3 seconds
        setTimeout(() => {
            if (successDiv.parentNode) {
                successDiv.remove();
            }
        }, 3000);
    }





    formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now - date;
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        if (diffDays === 0) {
            return 'Today';
        } else if (diffDays === 1) {
            return 'Yesterday';
        } else if (diffDays < 7) {
            return `${diffDays} days ago`;
        } else {
            return date.toLocaleDateString();
        }
    }

    async deleteAllChatHistory() {
        // Get reference to chat app for custom dialogs
        const chatApp = window.chatApp;
        if (!chatApp) {
            console.error('ChatApp instance not available');
            return;
        }

        // First confirmation - show the warning
        const firstConfirmed = await chatApp.showCustomConfirmDialog({
            title: 'Delete All Chat History',
            message: '⚠️ WARNING: This will permanently delete ALL your chat history!',
            description: 'This includes all conversations, messages, and chat data. This action CANNOT be undone.',
            confirmText: 'Continue',
            cancelText: 'Cancel',
            type: 'danger'
        });

        if (!firstConfirmed) return;

        // Second confirmation for extra safety
        const doubleConfirmed = await chatApp.showCustomConfirmDialog({
            title: 'Final Confirmation',
            message: 'Last chance! Are you really sure?',
            description: 'You are about to permanently delete ALL chat history. This action is irreversible.',
            confirmText: 'Yes, Delete Everything',
            cancelText: 'Cancel',
            type: 'danger'
        });

        if (!doubleConfirmed) return;

        // Third confirmation requiring typing "DELETE"
        const typedConfirmed = await chatApp.showCustomInputDialog({
            title: 'Type to Confirm',
            message: 'Type "DELETE" to confirm deletion of all chat history',
            description: 'This is your final confirmation. Type exactly "DELETE" in capital letters to proceed.',
            inputLabel: 'Confirmation text:',
            placeholder: 'Type DELETE here...',
            expectedValue: 'DELETE',
            confirmText: 'Delete Everything',
            cancelText: 'Cancel',
            type: 'danger'
        });

        if (!typedConfirmed) {
            chatApp.showNotification('Deletion cancelled. Chat history preserved.', 'info');
            return;
        }

        try {
            // Show loading state
            const deleteBtn = document.getElementById('deleteAllChatsBtn');
            deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Deleting...';
            deleteBtn.disabled = true;

            // Get reference to chat app for deletion
            const chatApp = window.chatApp;

            if (chatApp) {
                // Delete from database if using Supabase
                if (chatApp.useDatabase && chatApp.supabaseService.isAvailable()) {
                    try {
                        // Get current user ID
                        const userId = await chatApp.getCurrentUserId();

                        // Get all conversations for this user
                        const conversations = await chatApp.supabaseService.getAllConversations(userId);

                        // Delete each conversation (this will cascade delete messages)
                        for (const conversation of conversations) {
                            await chatApp.supabaseService.deleteConversation(conversation.id);
                        }

                        console.log(`Deleted ${conversations.length} conversations from database`);
                    } catch (error) {
                        console.error('Error deleting from database:', error);
                        throw error;
                    }
                }

                // Clear local chat data
                chatApp.chats.clear();
                chatApp.currentChatId = null;

                // Create a new chat to replace the deleted ones
                await chatApp.createNewChat();

                console.log('All chat history deleted successfully');
            }

            // Also clear any localStorage chat data (fallback storage)
            const localStorageKeys = Object.keys(localStorage);
            localStorageKeys.forEach(key => {
                if (key.startsWith('chat_') || key.includes('conversation') || key.includes('message')) {
                    localStorage.removeItem(key);
                }
            });

            // Show success message
            chatApp.showNotification('All chat history deleted successfully!', 'success');

            // Hide settings menu
            this.hideSettingsMenu();

        } catch (error) {
            console.error('Failed to delete chat history:', error);
            alert('Failed to delete chat history. Please try again or contact support.');
        } finally {
            // Restore button state
            const deleteBtn = document.getElementById('deleteAllChatsBtn');
            if (deleteBtn) {
                deleteBtn.innerHTML = '<i class="fas fa-trash-alt"></i> Delete All';
                deleteBtn.disabled = false;
            }
        }
    }
}

// Global references for cleanup
let globalChatApp = null;
let globalAuthManager = null;

// Global cleanup function
function cleanupVeritasApp() {
    try {
        if (globalChatApp && typeof globalChatApp.cleanup === 'function') {
            globalChatApp.cleanup();
        }
    } catch (error) {
        console.warn('Error during global cleanup:', error);
    }
}

// New streamlined global initialization
function initializeVeritasApp() {
    try {
        globalAuthManager = new AuthManager();



        // Check for orphaned memories
        window.checkOrphanedMemories = () => {
            if (window.chatApp) {
                window.chatApp.checkOrphanedMemories();
            } else {
                console.log('ChatApp not initialized yet');
            }
        };

        // Migrate orphaned memories
        window.migrateOrphanedMemories = () => {
            if (window.chatApp) {
                window.chatApp.migrateOrphanedMemories();
            } else {
                console.log('ChatApp not initialized yet');
            }
        };

        // Test text formatting
        window.testFormatting = () => {
            if (window.chatApp) {
                console.log('=== Testing Text Formatting ===');

                const testCases = [
                    "I know that *Claude* is a large language model",
                    "This is **bold** text with proper spacing",
                    "Mix of *italic* and **bold** formatting",
                    "Multiple *words* in *italic* format",
                    "Edge case*italic*without spaces",
                    "**Bold** at start and *italic* at end",
                    "Code `example` with formatting",
                    "Complex: **bold** and *italic* and `code` together"
                ];

                testCases.forEach((testCase, index) => {
                    const formatted = window.chatApp.formatMessage(testCase);
                    console.log(`${index + 1}. Input:  "${testCase}"`);
                    console.log(`   Output: "${formatted}"`);
                    console.log('---');
                });
            } else {
                console.log('ChatApp not initialized yet');
            }
        };

        // Force refresh HTML formatting
        window.refreshHTMLFormatting = () => {
            if (window.chatApp) {
                window.chatApp.forceRefreshCurrentChat();
                console.log('✅ HTML formatting refreshed for current chat');
            } else {
                console.log('ChatApp not initialized yet');
            }
        };

        // Test the specific spacing issue mentioned by user
        window.testSpacingFix = async () => {
            if (window.chatApp) {
                console.log('=== Testing Spacing Fix ===');

                // Simulate the exact issue: "I know thatClaudeis a large language model"
                const testMessage = "I know that *Claude* is a large language model, similar to me, developed by Anthropic.";

                console.log('Sending test message to check spacing...');
                console.log('Input:', testMessage);

                // Add the message to the chat to see the actual rendering
                const chat = window.chatApp.chats.get(window.chatApp.currentChatId);
                if (chat) {
                    const aiMessage = {
                        role: 'assistant',
                        content: testMessage,
                        timestamp: new Date()
                    };

                    chat.messages.push(aiMessage);
                    await window.chatApp.showTypingAnimation(aiMessage);

                    console.log('✅ Test message added to chat. Check the UI to see if spacing is preserved.');
                } else {
                    console.log('❌ No active chat found. Please create a chat first.');
                }
            } else {
                console.log('ChatApp not initialized yet');
            }
        };

        // Test conversation history context
        window.testConversationHistory = () => {
            if (window.chatApp) {
                const chat = window.chatApp.chats.get(window.chatApp.currentChatId);
                if (chat) {
                    console.log('🔍 Current conversation history:');
                    console.log(`Total messages: ${chat.messages.length}`);
                    chat.messages.forEach((msg, index) => {
                        console.log(`${index + 1}. ${msg.role}: ${msg.content.substring(0, 100)}...`);
                    });

                    console.log('\n✅ Conversation history is being stored correctly.');
                    console.log('💡 To test if the AI remembers context, send a follow-up message that references something from earlier in the conversation.');
                } else {
                    console.log('❌ No active chat found. Please create a chat first.');
                }
            } else {
                console.log('ChatApp not initialized yet');
            }
        };
    } catch (error) {
        console.error('Failed to initialize Veritas App:', error);
    }
}

// Modern DOM ready detection
if (document.readyState === 'loading') {
    document.onreadystatechange = () => {
        if (document.readyState === 'complete') {
            initializeVeritasApp();
        }
    };
} else {
    // DOM already loaded
    initializeVeritasApp();
}

// Global function for clearing pending documents
window.clearPendingDocuments = () => {
    if (window.chatApp && typeof window.chatApp.clearPendingDocuments === 'function') {
        window.chatApp.clearPendingDocuments();
    }
};

// Test function for AI-powered grounding functionality
window.testGrounding = async () => {
    if (!window.chatApp) {
        console.error('ChatApp not initialized');
        return;
    }

    console.log('🤖 Testing AI-Powered Google Search Grounding Decision System...');

    // Test queries that should trigger grounding (current, real-time info)
    const shouldGroundQueries = [
        "What is the current stock price of Tesla?",
        "Who won the latest World Cup final?",
        "What are today's cryptocurrency prices?",
        "What is the weather forecast for today?",
        "What happened in the recent election results?",
        "What is the current Bitcoin price?",
        "Latest news about AI developments in 2024"
    ];

    // Test queries that should NOT trigger grounding (general knowledge, philosophical)
    const shouldNotGroundQueries = [
        "What is the meaning of life?",
        "Explain how photosynthesis works",
        "What is the capital of France?",
        "How do you calculate the area of a circle?",
        "Tell me about Shakespeare",
        "What are the principles of democracy?",
        "Explain quantum physics",
        "What is artificial intelligence?",
        "Research the history of Rome",
        "Analyze the concept of love"
    ];

    const researchAgent = window.chatApp.multiAgentSystem?.agents?.get('research');
    if (!researchAgent) {
        console.error('Research agent not found');
        return;
    }

    console.log('🔍 Testing queries that SHOULD use grounding (AI will decide):');
    for (const query of shouldGroundQueries) {
        try {
            const shouldGround = await researchAgent.shouldUseGrounding(query);
            console.log(`"${query.substring(0, 50)}..." -> AI Decision: ${shouldGround ? '✅ USE GROUNDING' : '❌ NO GROUNDING'}`);
        } catch (error) {
            console.log(`"${query.substring(0, 50)}..." -> Error: ${error.message}`);
        }
    }

    console.log('\n🧠 Testing queries that should NOT use grounding (AI will decide):');
    for (const query of shouldNotGroundQueries) {
        try {
            const shouldGround = await researchAgent.shouldUseGrounding(query);
            console.log(`"${query.substring(0, 50)}..." -> AI Decision: ${shouldGround ? '✅ USE GROUNDING' : '❌ NO GROUNDING'}`);
        } catch (error) {
            console.log(`"${query.substring(0, 50)}..." -> Error: ${error.message}`);
        }
    }

    console.log('\n🤖 AI-powered grounding test complete! The AI made intelligent decisions for each query.');
};



// Simple cleanup on page hide
window.onbeforeunload = () => {
    if (globalChatApp && typeof globalChatApp.cleanup === 'function') {
        globalChatApp.cleanup();
    }
};
